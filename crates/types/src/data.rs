use chrono::Utc;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 交易日志信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Transaction {
    pub timestamp: u64,
    pub macid: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    ///卡内车型
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,
    /// 收费车型
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_fee_type: Option<String>,
    /// 应收金额 （分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recievable_fee: Option<u32>,
    /// 实收金额 （分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_fee: Option<u32>,
    /// 本省应收金额 （分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_recievable_fee: Option<u32>,
    /// 本省实收金额 （分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_actual_fee: Option<u32>,
}

impl Transaction {
    pub fn new(macid: String) -> Self {
        Self {
            timestamp: Utc::now().timestamp_millis() as u64,
            macid,
            plate_no: None,
            vehicle_type: None,
            vehicle_fee_type: None,
            recievable_fee: None,
            actual_fee: None,
            pro_recievable_fee: None,
            pro_actual_fee: None,
        }
    }
}

/// 摄像头抓拍车辆信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VehicleData {
    pub timestamp: u64,
    pub camera_code: String,
    pub plate_no: Option<String>,
    pub plate_type: Option<String>,
    pub plate_color: Option<String>,
    pub vehicle_type: Option<String>,
    pub vehicle_cat: Option<String>,
    pub vehicle_size: Option<String>,
    pub vehicle_object: Option<String>,
    pub image_bytes: Option<Vec<u8>>,
}

impl VehicleData {
    pub fn new(camera_code: String) -> Self {
        Self {
            timestamp: Utc::now().timestamp_millis() as u64,
            camera_code,
            plate_no: None,
            plate_type: None,
            plate_color: None,
            vehicle_type: None,
            vehicle_cat: None,
            vehicle_size: None,
            vehicle_object: None,
            image_bytes: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NormalizedData {
    pub id: String,
    pub gantry_code: String,
    pub timestamp: u64,
    /// 车辆信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,
    /// 抓拍信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub camera_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_color: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_cat: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_size: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_object: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub image_bytes: Option<Vec<u8>>,
    /// 收费信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub macid: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_fee_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_actual_fee: Option<u32>,
}

impl NormalizedData {
    pub fn new(gantry_code: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: Utc::now().timestamp_millis() as u64,
            plate_no: None,
            vehicle_type: None,
            camera_code: None,
            plate_type: None,
            plate_color: None,
            vehicle_cat: None,
            vehicle_size: None,
            vehicle_object: None,
            image_bytes: None,
            macid: None,
            vehicle_fee_type: None,
            recievable_fee: None,
            actual_fee: None,
            pro_recievable_fee: None,
            pro_actual_fee: None,
        }
    }

    pub fn from_transaction(gantry_code: String, transaction: Transaction) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: transaction.timestamp,
            plate_no: transaction.plate_no,
            vehicle_type: transaction.vehicle_type,
            camera_code: None,
            plate_type: None,
            plate_color: None,
            vehicle_cat: None,
            vehicle_size: None,
            vehicle_object: None,
            image_bytes: None,
            macid: Some(transaction.macid),
            vehicle_fee_type: transaction.vehicle_fee_type,
            recievable_fee: transaction.recievable_fee,
            actual_fee: transaction.actual_fee,
            pro_recievable_fee: transaction.pro_recievable_fee,
            pro_actual_fee: transaction.pro_actual_fee,
        }
    }

    pub fn from_vehicle_data(gantry_code: String, vehicle_data: VehicleData) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: vehicle_data.timestamp,
            plate_no: vehicle_data.plate_no,
            vehicle_type: vehicle_data.vehicle_type,
            camera_code: Some(vehicle_data.camera_code),
            plate_type: vehicle_data.plate_type,
            plate_color: vehicle_data.plate_color,
            vehicle_cat: vehicle_data.vehicle_cat,
            vehicle_size: vehicle_data.vehicle_size,
            vehicle_object: vehicle_data.vehicle_object,
            image_bytes: vehicle_data.image_bytes,
            macid: None,
            vehicle_fee_type: None,
            recievable_fee: None,
            actual_fee: None,
            pro_recievable_fee: None,
            pro_actual_fee: None,
        }
    }

    pub fn is_transaction(&self) -> bool {
        self.macid.is_some()
    }

    pub fn is_vehicle_data(&self) -> bool {
        self.camera_code.is_some()
    }
}
