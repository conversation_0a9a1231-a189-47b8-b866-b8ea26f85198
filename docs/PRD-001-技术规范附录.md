# PRD-001 技术规范附录

**文档编号：** PRD-001-TECH  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**关联文档：** PRD-001 数据采集系统产品需求文档  

---

## 1. 详细接口规范

### 1.1 数据采集器接口

```rust
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::mpsc;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataSource {
    LogFile,
    SftpRemote,
    CameraSDK,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RawData {
    pub source: DataSource,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub content: Vec<u8>,
    pub metadata: HashMap<String, String>,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub enum CollectorStatus {
    Stopped,
    Starting,
    Running,
    Error(String),
}

#[derive(Debug, thiserror::Error)]
pub enum CollectorError {
    #[error("Configuration error: {0}")]
    Config(String),
    #[error("Network error: {0}")]
    Network(String),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("SDK error: {0}")]
    Sdk(String),
}

pub type DataCallback = Box<dyn Fn(RawData) -> Result<(), CollectorError> + Send + Sync>;

#[async_trait]
pub trait DataCollector: Send + Sync {
    /// 启动数据采集器
    async fn start(&mut self) -> Result<(), CollectorError>;
    
    /// 停止数据采集器
    async fn stop(&mut self) -> Result<(), CollectorError>;
    
    /// 获取采集器状态
    async fn get_status(&self) -> CollectorStatus;
    
    /// 订阅数据回调
    fn subscribe_data(&self, callback: DataCallback) -> Result<(), CollectorError>;
    
    /// 获取采集器配置
    fn get_config(&self) -> &dyn CollectorConfig;
}
```

### 1.2 日志采集器实现

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogCollectorConfig {
    pub watch_directory: String,
    pub file_pattern: String,
    pub mode: CollectorMode,
    pub buffer_size: usize,
    pub max_file_size: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CollectorMode {
    Passive,  // 监听文件变化
    Active,   // 定时扫描
}

pub struct LogCollector {
    config: LogCollectorConfig,
    status: Arc<RwLock<CollectorStatus>>,
    data_sender: Option<mpsc::UnboundedSender<RawData>>,
    watcher: Option<notify::RecommendedWatcher>,
}

impl LogCollector {
    pub fn new(config: LogCollectorConfig) -> Self {
        Self {
            config,
            status: Arc::new(RwLock::new(CollectorStatus::Stopped)),
            data_sender: None,
            watcher: None,
        }
    }
    
    async fn setup_file_watcher(&mut self) -> Result<(), CollectorError> {
        let (tx, mut rx) = mpsc::unbounded_channel();
        
        let watcher = notify::recommended_watcher(move |res| {
            match res {
                Ok(event) => {
                    if let Err(e) = tx.send(event) {
                        eprintln!("Failed to send file event: {}", e);
                    }
                }
                Err(e) => eprintln!("File watch error: {:?}", e),
            }
        })?;
        
        // 监听目录变化
        watcher.watch(
            Path::new(&self.config.watch_directory),
            notify::RecursiveMode::NonRecursive,
        )?;
        
        self.watcher = Some(watcher);
        
        // 处理文件事件
        let config = self.config.clone();
        let data_sender = self.data_sender.clone();
        
        tokio::spawn(async move {
            while let Some(event) = rx.recv().await {
                if let Err(e) = Self::handle_file_event(event, &config, &data_sender).await {
                    eprintln!("Failed to handle file event: {}", e);
                }
            }
        });
        
        Ok(())
    }
    
    async fn handle_file_event(
        event: notify::Event,
        config: &LogCollectorConfig,
        data_sender: &Option<mpsc::UnboundedSender<RawData>>,
    ) -> Result<(), CollectorError> {
        use notify::EventKind;
        
        match event.kind {
            EventKind::Create(_) | EventKind::Modify(_) => {
                for path in event.paths {
                    if Self::matches_pattern(&path, &config.file_pattern) {
                        let raw_data = Self::read_file_incremental(&path).await?;
                        
                        if let Some(sender) = data_sender {
                            sender.send(raw_data)?;
                        }
                    }
                }
            }
            _ => {}
        }
        
        Ok(())
    }
    
    fn matches_pattern(path: &Path, pattern: &str) -> bool {
        if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
            glob::Pattern::new(pattern)
                .map(|p| p.matches(filename))
                .unwrap_or(false)
        } else {
            false
        }
    }
    
    async fn read_file_incremental(path: &Path) -> Result<RawData, CollectorError> {
        let content = tokio::fs::read(path).await?;
        let metadata = Self::extract_file_metadata(path).await?;
        
        Ok(RawData {
            source: DataSource::LogFile,
            timestamp: chrono::Utc::now(),
            content,
            metadata,
        })
    }
    
    async fn extract_file_metadata(path: &Path) -> Result<HashMap<String, String>, CollectorError> {
        let mut metadata = HashMap::new();
        
        if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
            metadata.insert("filename".to_string(), filename.to_string());
        }
        
        if let Ok(file_metadata) = tokio::fs::metadata(path).await {
            metadata.insert("size".to_string(), file_metadata.len().to_string());
            
            if let Ok(modified) = file_metadata.modified() {
                if let Ok(datetime) = modified.duration_since(std::time::UNIX_EPOCH) {
                    metadata.insert("modified".to_string(), datetime.as_secs().to_string());
                }
            }
        }
        
        Ok(metadata)
    }
}

#[async_trait]
impl DataCollector for LogCollector {
    async fn start(&mut self) -> Result<(), CollectorError> {
        let mut status = self.status.write().await;
        *status = CollectorStatus::Starting;
        
        let (tx, _rx) = mpsc::unbounded_channel();
        self.data_sender = Some(tx);
        
        match self.config.mode {
            CollectorMode::Passive => {
                self.setup_file_watcher().await?;
            }
            CollectorMode::Active => {
                self.setup_periodic_scan().await?;
            }
        }
        
        *status = CollectorStatus::Running;
        Ok(())
    }
    
    async fn stop(&mut self) -> Result<(), CollectorError> {
        let mut status = self.status.write().await;
        *status = CollectorStatus::Stopped;
        
        self.watcher = None;
        self.data_sender = None;
        
        Ok(())
    }
    
    async fn get_status(&self) -> CollectorStatus {
        self.status.read().await.clone()
    }
    
    fn subscribe_data(&self, callback: DataCallback) -> Result<(), CollectorError> {
        // 实现数据订阅逻辑
        Ok(())
    }
    
    fn get_config(&self) -> &dyn CollectorConfig {
        &self.config
    }
}
```

### 1.3 SFTP采集器实现

```rust
use ssh2::Session;
use std::net::TcpStream;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SftpCollectorConfig {
    pub host: String,
    pub port: u16,
    pub username: String,
    pub private_key_path: String,
    pub remote_directory: String,
    pub sync_interval: u64,
    pub max_concurrent_downloads: usize,
}

pub struct SftpCollector {
    config: SftpCollectorConfig,
    status: Arc<RwLock<CollectorStatus>>,
    session: Option<Session>,
    last_sync_time: Arc<RwLock<chrono::DateTime<chrono::Utc>>>,
}

impl SftpCollector {
    pub fn new(config: SftpCollectorConfig) -> Self {
        Self {
            config,
            status: Arc::new(RwLock::new(CollectorStatus::Stopped)),
            session: None,
            last_sync_time: Arc::new(RwLock::new(chrono::Utc::now())),
        }
    }
    
    async fn establish_connection(&mut self) -> Result<(), CollectorError> {
        let tcp = TcpStream::connect(format!("{}:{}", self.config.host, self.config.port))?;
        let mut session = Session::new()?;
        session.set_tcp_stream(tcp);
        session.handshake()?;
        
        // 使用私钥认证
        session.userauth_pubkey_file(
            &self.config.username,
            None,
            Path::new(&self.config.private_key_path),
            None,
        )?;
        
        if !session.authenticated() {
            return Err(CollectorError::Network("Authentication failed".to_string()));
        }
        
        self.session = Some(session);
        Ok(())
    }
    
    async fn sync_remote_files(&self) -> Result<Vec<RawData>, CollectorError> {
        let session = self.session.as_ref()
            .ok_or_else(|| CollectorError::Network("No active session".to_string()))?;
        
        let sftp = session.sftp()?;
        let remote_files = sftp.readdir(Path::new(&self.config.remote_directory))?;
        
        let mut results = Vec::new();
        let last_sync = *self.last_sync_time.read().await;
        
        for (path, stat) in remote_files {
            if stat.is_file() {
                // 检查文件修改时间
                if let Some(mtime) = stat.mtime {
                    let file_time = chrono::DateTime::from_timestamp(mtime as i64, 0)
                        .unwrap_or(chrono::Utc::now());
                    
                    if file_time > last_sync {
                        let raw_data = self.download_file(&sftp, &path).await?;
                        results.push(raw_data);
                    }
                }
            }
        }
        
        // 更新同步时间
        *self.last_sync_time.write().await = chrono::Utc::now();
        
        Ok(results)
    }
    
    async fn download_file(
        &self,
        sftp: &ssh2::Sftp,
        remote_path: &Path,
    ) -> Result<RawData, CollectorError> {
        let mut remote_file = sftp.open(remote_path)?;
        let mut content = Vec::new();
        remote_file.read_to_end(&mut content)?;
        
        let mut metadata = HashMap::new();
        if let Some(filename) = remote_path.file_name().and_then(|n| n.to_str()) {
            metadata.insert("filename".to_string(), filename.to_string());
            metadata.insert("remote_path".to_string(), remote_path.to_string_lossy().to_string());
        }
        
        Ok(RawData {
            source: DataSource::SftpRemote,
            timestamp: chrono::Utc::now(),
            content,
            metadata,
        })
    }
}
```

## 2. 基于实际代码的数据模型设计

### 2.1 核心数据类型（基于crates/types）

#### 2.1.1 交易数据模型

```rust
use chrono::Utc;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 交易日志信息（来自实际代码）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Transaction {
    pub timestamp: u64,
    pub macid: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    /// 卡内车型
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,
    /// 收费车型
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_fee_type: Option<String>,
    /// 应收金额（分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recievable_fee: Option<u32>,
    /// 实收金额（分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_fee: Option<u32>,
    /// 本省应收金额（分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_recievable_fee: Option<u32>,
    /// 本省实收金额（分）
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_actual_fee: Option<u32>,
}

impl Transaction {
    pub fn new(macid: String) -> Self {
        Self {
            timestamp: Utc::now().timestamp_millis() as u64,
            macid,
            plate_no: None,
            vehicle_type: None,
            vehicle_fee_type: None,
            recievable_fee: None,
            actual_fee: None,
            pro_recievable_fee: None,
            pro_actual_fee: None,
        }
    }
}
```

#### 2.1.2 摄像头数据模型

```rust
/// 摄像头抓拍车辆信息（来自实际代码）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VehicleData {
    pub timestamp: u64,
    pub camera_code: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_color: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_cat: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_size: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_object: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub image_bytes: Option<Vec<u8>>,
}

impl VehicleData {
    pub fn new(camera_code: String) -> Self {
        Self {
            timestamp: Utc::now().timestamp_millis() as u64,
            camera_code,
            plate_no: None,
            plate_type: None,
            plate_color: None,
            vehicle_type: None,
            vehicle_cat: None,
            vehicle_size: None,
            vehicle_object: None,
            image_bytes: None,
        }
    }
}
```

#### 2.1.3 归一化数据模型

```rust
/// 归一化数据结构（来自实际代码）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NormalizedData {
    pub id: String,
    pub gantry_code: String,
    pub timestamp: u64,

    // 车辆基础信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,

    // 抓拍相关信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub camera_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_color: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_cat: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_size: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_object: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub image_bytes: Option<Vec<u8>>,

    // 收费相关信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub macid: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_fee_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_actual_fee: Option<u32>,
}

impl NormalizedData {
    pub fn new(gantry_code: String) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: Utc::now().timestamp_millis() as u64,
            plate_no: None,
            vehicle_type: None,
            camera_code: None,
            plate_type: None,
            plate_color: None,
            vehicle_cat: None,
            vehicle_size: None,
            vehicle_object: None,
            image_bytes: None,
            macid: None,
            vehicle_fee_type: None,
            recievable_fee: None,
            actual_fee: None,
            pro_recievable_fee: None,
            pro_actual_fee: None,
        }
    }

    pub fn from_transaction(gantry_code: String, transaction: Transaction) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: transaction.timestamp,
            plate_no: transaction.plate_no,
            vehicle_type: transaction.vehicle_type,
            camera_code: None,
            plate_type: None,
            plate_color: None,
            vehicle_cat: None,
            vehicle_size: None,
            vehicle_object: None,
            image_bytes: None,
            macid: Some(transaction.macid),
            vehicle_fee_type: transaction.vehicle_fee_type,
            recievable_fee: transaction.recievable_fee,
            actual_fee: transaction.actual_fee,
            pro_recievable_fee: transaction.pro_recievable_fee,
            pro_actual_fee: transaction.pro_actual_fee,
        }
    }

    pub fn from_vehicle_data(gantry_code: String, vehicle_data: VehicleData) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: vehicle_data.timestamp,
            plate_no: vehicle_data.plate_no,
            vehicle_type: vehicle_data.vehicle_type,
            camera_code: Some(vehicle_data.camera_code),
            plate_type: vehicle_data.plate_type,
            plate_color: vehicle_data.plate_color,
            vehicle_cat: vehicle_data.vehicle_cat,
            vehicle_size: vehicle_data.vehicle_size,
            vehicle_object: vehicle_data.vehicle_object,
            image_bytes: vehicle_data.image_bytes,
            macid: None,
            vehicle_fee_type: None,
            recievable_fee: None,
            actual_fee: None,
            pro_recievable_fee: None,
            pro_actual_fee: None,
        }
    }

    pub fn is_transaction(&self) -> bool {
        self.macid.is_some()
    }

    pub fn is_vehicle_data(&self) -> bool {
        self.camera_code.is_some()
    }
}
```

#### 2.1.4 数据采集器接口

```rust
use anyhow::Result;

/// 统一的数据采集器接口（来自实际代码）
pub trait Collector {
    fn collect(&mut self) -> Result<Option<Vec<NormalizedData>>>;
}
```

## 3. 数据传输模块详细实现（transport crate）

### 3.1 传输模块核心接口

```rust
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use thiserror::Error;
use tokio::sync::mpsc;
use super::NormalizedData;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportType {
    Serial,
    TcpClient,
    TcpServer,
    Udp,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportStatus {
    Disconnected,
    Connecting,
    Connected,
    Error(String),
}

#[derive(Debug, Error)]
pub enum TransportError {
    #[error("Connection error: {0}")]
    Connection(String),
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("Serialization error: {0}")]
    Serialization(#[from] serde_json::Error),
    #[error("Timeout error")]
    Timeout,
    #[error("Protocol error: {0}")]
    Protocol(String),
}

#[async_trait]
pub trait DataTransport: Send + Sync {
    async fn connect(&mut self) -> Result<(), TransportError>;
    async fn disconnect(&mut self) -> Result<(), TransportError>;
    async fn send(&mut self, data: &NormalizedData) -> Result<(), TransportError>;
    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<(), TransportError>;
    async fn get_status(&self) -> TransportStatus;
    fn get_transport_type(&self) -> TransportType;
}
```

### 3.2 串口传输实现

```rust
use serialport::{SerialPort, SerialPortBuilder};
use std::sync::{Arc, Mutex};
use std::time::Duration;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerialConfig {
    pub port: String,
    pub baud_rate: u32,
    pub data_bits: u8,
    pub stop_bits: u8,
    pub parity: String,  // "none", "odd", "even"
    pub flow_control: String,  // "none", "hardware", "software"
    pub timeout: u64,  // milliseconds
}

pub struct SerialTransport {
    config: SerialConfig,
    port: Option<Arc<Mutex<Box<dyn SerialPort>>>>,
    status: TransportStatus,
}

impl SerialTransport {
    pub fn new(config: SerialConfig) -> Self {
        Self {
            config,
            port: None,
            status: TransportStatus::Disconnected,
        }
    }

    fn build_frame(&self, data: &[u8]) -> Vec<u8> {
        let mut frame = Vec::new();

        // 帧头 (0xAA55)
        frame.extend_from_slice(&[0xAA, 0x55]);

        // 数据长度 (4字节，大端序)
        let len = data.len() as u32;
        frame.extend_from_slice(&len.to_be_bytes());

        // 数据内容
        frame.extend_from_slice(data);

        // CRC16校验
        let crc = self.calculate_crc16(&frame[6..]);
        frame.extend_from_slice(&crc.to_be_bytes());

        // 帧尾 (0x55AA)
        frame.extend_from_slice(&[0x55, 0xAA]);

        frame
    }

    fn calculate_crc16(&self, data: &[u8]) -> u16 {
        let mut crc: u16 = 0xFFFF;
        for byte in data {
            crc ^= *byte as u16;
            for _ in 0..8 {
                if crc & 1 != 0 {
                    crc = (crc >> 1) ^ 0xA001;
                } else {
                    crc >>= 1;
                }
            }
        }
        crc
    }
}

#[async_trait]
impl DataTransport for SerialTransport {
    async fn connect(&mut self) -> Result<(), TransportError> {
        self.status = TransportStatus::Connecting;

        let mut builder = serialport::new(&self.config.port, self.config.baud_rate);

        // 配置串口参数
        builder = builder.data_bits(match self.config.data_bits {
            7 => serialport::DataBits::Seven,
            8 => serialport::DataBits::Eight,
            _ => serialport::DataBits::Eight,
        });

        builder = builder.stop_bits(match self.config.stop_bits {
            1 => serialport::StopBits::One,
            2 => serialport::StopBits::Two,
            _ => serialport::StopBits::One,
        });

        builder = builder.parity(match self.config.parity.as_str() {
            "odd" => serialport::Parity::Odd,
            "even" => serialport::Parity::Even,
            _ => serialport::Parity::None,
        });

        builder = builder.timeout(Duration::from_millis(self.config.timeout));

        match builder.open() {
            Ok(port) => {
                self.port = Some(Arc::new(Mutex::new(port)));
                self.status = TransportStatus::Connected;
                Ok(())
            }
            Err(e) => {
                self.status = TransportStatus::Error(e.to_string());
                Err(TransportError::Connection(e.to_string()))
            }
        }
    }

    async fn disconnect(&mut self) -> Result<(), TransportError> {
        self.port = None;
        self.status = TransportStatus::Disconnected;
        Ok(())
    }

    async fn send(&mut self, data: &NormalizedData) -> Result<(), TransportError> {
        if let Some(port) = &self.port {
            let json_data = serde_json::to_vec(data)?;
            let frame = self.build_frame(&json_data);

            let mut port_guard = port.lock().unwrap();
            port_guard.write_all(&frame)?;
            port_guard.flush()?;

            Ok(())
        } else {
            Err(TransportError::Connection("Not connected".to_string()))
        }
    }

    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<(), TransportError> {
        for item in data {
            self.send(item).await?;
        }
        Ok(())
    }

    async fn get_status(&self) -> TransportStatus {
        self.status.clone()
    }

    fn get_transport_type(&self) -> TransportType {
        TransportType::Serial
    }
}
```

### 3.3 TCP网络传输实现

```rust
use tokio::net::{TcpStream, TcpListener};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use std::net::SocketAddr;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TcpClientConfig {
    pub name: String,
    pub host: String,
    pub port: u16,
    pub connect_timeout: u64,
    pub read_timeout: u64,
    pub retry_attempts: u32,
    pub heartbeat_interval: u64,
}

pub struct TcpClientTransport {
    config: TcpClientConfig,
    stream: Option<TcpStream>,
    status: TransportStatus,
}

impl TcpClientTransport {
    pub fn new(config: TcpClientConfig) -> Self {
        Self {
            config,
            stream: None,
            status: TransportStatus::Disconnected,
        }
    }

    fn build_packet(&self, data: &[u8]) -> Vec<u8> {
        let mut packet = Vec::new();

        // 包头 (0x48435300 = "HCS\0")
        packet.extend_from_slice(&[0x48, 0x43, 0x53, 0x00]);

        // 版本 (0x01)
        packet.push(0x01);

        // 数据长度 (4字节，大端序)
        let len = data.len() as u32;
        packet.extend_from_slice(&len.to_be_bytes());

        // 数据类型 (0x01 = JSON)
        packet.push(0x01);

        // 数据内容
        packet.extend_from_slice(data);

        // CRC32校验
        let crc = self.calculate_crc32(&packet[10..]);
        packet.extend_from_slice(&crc.to_be_bytes());

        packet
    }

    fn calculate_crc32(&self, data: &[u8]) -> u32 {
        let mut crc = crc32fast::Hasher::new();
        crc.update(data);
        crc.finalize()
    }
}

#[async_trait]
impl DataTransport for TcpClientTransport {
    async fn connect(&mut self) -> Result<(), TransportError> {
        self.status = TransportStatus::Connecting;

        let addr = format!("{}:{}", self.config.host, self.config.port);
        let socket_addr: SocketAddr = addr.parse()
            .map_err(|e| TransportError::Connection(format!("Invalid address: {}", e)))?;

        match tokio::time::timeout(
            Duration::from_secs(self.config.connect_timeout),
            TcpStream::connect(socket_addr)
        ).await {
            Ok(Ok(stream)) => {
                self.stream = Some(stream);
                self.status = TransportStatus::Connected;
                Ok(())
            }
            Ok(Err(e)) => {
                self.status = TransportStatus::Error(e.to_string());
                Err(TransportError::Connection(e.to_string()))
            }
            Err(_) => {
                self.status = TransportStatus::Error("Connection timeout".to_string());
                Err(TransportError::Timeout)
            }
        }
    }

    async fn disconnect(&mut self) -> Result<(), TransportError> {
        if let Some(mut stream) = self.stream.take() {
            let _ = stream.shutdown().await;
        }
        self.status = TransportStatus::Disconnected;
        Ok(())
    }

    async fn send(&mut self, data: &NormalizedData) -> Result<(), TransportError> {
        if let Some(stream) = &mut self.stream {
            let json_data = serde_json::to_vec(data)?;
            let packet = self.build_packet(&json_data);

            stream.write_all(&packet).await?;
            stream.flush().await?;

            // 等待确认响应
            let mut response = [0u8; 4];
            match tokio::time::timeout(
                Duration::from_secs(self.config.read_timeout),
                stream.read_exact(&mut response)
            ).await {
                Ok(Ok(_)) => {
                    if response == [0x4F, 0x4B, 0x00, 0x00] { // "OK\0\0"
                        Ok(())
                    } else {
                        Err(TransportError::Protocol("Invalid response".to_string()))
                    }
                }
                Ok(Err(e)) => Err(TransportError::Io(e)),
                Err(_) => Err(TransportError::Timeout),
            }
        } else {
            Err(TransportError::Connection("Not connected".to_string()))
        }
    }

    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<(), TransportError> {
        // 批量发送实现
        let batch_json = serde_json::to_vec(data)?;
        let packet = self.build_packet(&batch_json);

        if let Some(stream) = &mut self.stream {
            stream.write_all(&packet).await?;
            stream.flush().await?;
            Ok(())
        } else {
            Err(TransportError::Connection("Not connected".to_string()))
        }
    }

    async fn get_status(&self) -> TransportStatus {
        self.status.clone()
    }

    fn get_transport_type(&self) -> TransportType {
        TransportType::TcpClient
    }
}
```

### 3.4 传输管理器实现

```rust
use std::collections::HashMap;
use tokio::sync::mpsc;
use tokio::time::{interval, Duration};

pub struct TransportManager {
    transports: HashMap<String, Box<dyn DataTransport>>,
    data_receiver: mpsc::UnboundedReceiver<NormalizedData>,
    data_sender: mpsc::UnboundedSender<NormalizedData>,
    config: TransportConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransportConfig {
    pub serial: Option<SerialTransportConfig>,
    pub network: Option<NetworkTransportConfig>,
    pub batch_config: BatchConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SerialTransportConfig {
    pub enabled: bool,
    pub devices: Vec<SerialConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkTransportConfig {
    pub tcp_clients: Vec<TcpClientConfig>,
    pub udp_clients: Vec<UdpClientConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchConfig {
    pub enabled: bool,
    pub batch_size: usize,
    pub batch_timeout: u64,  // milliseconds
    pub partial_ack: bool,
}

impl TransportManager {
    pub fn new(config: TransportConfig) -> Self {
        let (data_sender, data_receiver) = mpsc::unbounded_channel();

        Self {
            transports: HashMap::new(),
            data_receiver,
            data_sender,
            config,
        }
    }

    pub async fn initialize(&mut self) -> Result<(), TransportError> {
        // 初始化串口传输
        if let Some(serial_config) = &self.config.serial {
            if serial_config.enabled {
                for (i, device_config) in serial_config.devices.iter().enumerate() {
                    let transport = SerialTransport::new(device_config.clone());
                    let name = format!("serial_{}", i);
                    self.transports.insert(name, Box::new(transport));
                }
            }
        }

        // 初始化网络传输
        if let Some(network_config) = &self.config.network {
            for (i, client_config) in network_config.tcp_clients.iter().enumerate() {
                let transport = TcpClientTransport::new(client_config.clone());
                let name = format!("tcp_client_{}", i);
                self.transports.insert(name, Box::new(transport));
            }
        }

        // 连接所有传输通道
        for (name, transport) in &mut self.transports {
            if let Err(e) = transport.connect().await {
                tracing::error!("Failed to connect transport {}: {}", name, e);
            }
        }

        Ok(())
    }

    pub fn get_data_sender(&self) -> mpsc::UnboundedSender<NormalizedData> {
        self.data_sender.clone()
    }

    pub async fn start(&mut self) -> Result<(), TransportError> {
        let mut batch_buffer = Vec::new();
        let mut batch_timer = interval(Duration::from_millis(self.config.batch_config.batch_timeout));

        loop {
            tokio::select! {
                // 接收新数据
                data = self.data_receiver.recv() => {
                    if let Some(data) = data {
                        if self.config.batch_config.enabled {
                            batch_buffer.push(data);

                            // 检查是否达到批量大小
                            if batch_buffer.len() >= self.config.batch_config.batch_size {
                                self.send_batch(&batch_buffer).await?;
                                batch_buffer.clear();
                            }
                        } else {
                            self.send_single(&data).await?;
                        }
                    }
                }

                // 批量超时处理
                _ = batch_timer.tick() => {
                    if !batch_buffer.is_empty() {
                        self.send_batch(&batch_buffer).await?;
                        batch_buffer.clear();
                    }
                }
            }
        }
    }

    async fn send_single(&mut self, data: &NormalizedData) -> Result<(), TransportError> {
        let mut errors = Vec::new();

        for (name, transport) in &mut self.transports {
            if let Err(e) = transport.send(data).await {
                tracing::error!("Failed to send data via {}: {}", name, e);
                errors.push((name.clone(), e));
            }
        }

        if errors.len() == self.transports.len() {
            return Err(TransportError::Connection("All transports failed".to_string()));
        }

        Ok(())
    }

    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<(), TransportError> {
        let mut errors = Vec::new();

        for (name, transport) in &mut self.transports {
            if let Err(e) = transport.send_batch(data).await {
                tracing::error!("Failed to send batch via {}: {}", name, e);
                errors.push((name.clone(), e));
            }
        }

        if errors.len() == self.transports.len() {
            return Err(TransportError::Connection("All transports failed".to_string()));
        }

        Ok(())
    }

    pub async fn get_status(&self) -> HashMap<String, TransportStatus> {
        let mut status_map = HashMap::new();

        for (name, transport) in &self.transports {
            let status = transport.get_status().await;
            status_map.insert(name.clone(), status);
        }

        status_map
    }
}
```

## 4. 配置管理系统

### 4.1 配置结构定义

```rust
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HcsConfig {
    pub application: ApplicationConfig,
    pub collectors: CollectorsConfig,
    pub transport: TransportConfig,
    pub storage: StorageConfig,
    pub system: SystemConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApplicationConfig {
    pub name: String,
    pub version: String,
    pub gantry_code: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CollectorsConfig {
    pub transaction: TransactionCollectorConfig,
    pub image: ImageCollectorConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransactionCollectorConfig {
    pub enabled: bool,
    pub mode: String,  // "passive" | "active"
    pub watch_directory: String,
    pub file_pattern: String,
    pub buffer_size: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageCollectorConfig {
    pub enabled: bool,
    pub sdk_config: SdkConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SdkConfig {
    pub library_path: String,
    pub connection_timeout: u64,
    pub devices: Vec<CameraDevice>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CameraDevice {
    pub camera_code: String,
    pub ip: String,
    pub port: u16,
    pub username: String,
    pub password: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    pub local: LocalStorageConfig,
    pub database: Option<DatabaseConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LocalStorageConfig {
    pub enabled: bool,
    pub data_directory: String,
    pub max_file_size: String,
    pub retention_days: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub enabled: bool,
    pub connection_string: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfig {
    pub log_level: String,
    pub metrics_enabled: bool,
    pub health_check_port: u16,
}

impl HcsConfig {
    pub fn load_from_file(path: &str) -> Result<Self, ConfigError> {
        let content = std::fs::read_to_string(path)?;
        let config: HcsConfig = toml::from_str(&content)?;
        config.validate()?;
        Ok(config)
    }

    pub fn validate(&self) -> Result<(), ConfigError> {
        // 验证门架编码
        if self.application.gantry_code.is_empty() {
            return Err(ConfigError::InvalidConfig("Gantry code cannot be empty".to_string()));
        }

        // 验证采集器配置
        if self.collectors.transaction.enabled && self.collectors.transaction.watch_directory.is_empty() {
            return Err(ConfigError::InvalidConfig("Transaction watch directory cannot be empty".to_string()));
        }

        // 验证传输配置
        if let Some(serial_config) = &self.transport.serial {
            if serial_config.enabled && serial_config.devices.is_empty() {
                return Err(ConfigError::InvalidConfig("Serial devices cannot be empty when enabled".to_string()));
            }
        }

        Ok(())
    }
}

#[derive(Debug, thiserror::Error)]
pub enum ConfigError {
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
    #[error("TOML parse error: {0}")]
    Toml(#[from] toml::de::Error),
    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),
}
```
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct ImageRecord {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub device_id: String,
    pub image_path: String,
    pub image_size: i64,
    pub resolution: Resolution,
    pub quality_score: u8,
    pub location: Option<GpsCoordinate>,
    pub metadata: serde_json::Value,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "resolution")]
pub struct Resolution {
    pub width: u32,
    pub height: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "gps_coordinate")]
pub struct GpsCoordinate {
    pub latitude: f64,
    pub longitude: f64,
    pub altitude: Option<f64>,
}

impl ImageRecord {
    pub fn new(
        device_id: String,
        image_path: String,
        image_size: i64,
        resolution: Resolution,
        quality_score: u8,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            device_id,
            image_path,
            image_size,
            resolution,
            quality_score,
            location: None,
            metadata: serde_json::Value::Object(serde_json::Map::new()),
            created_at: chrono::Utc::now(),
        }
    }
    
    pub fn set_location(&mut self, lat: f64, lon: f64, alt: Option<f64>) {
        self.location = Some(GpsCoordinate {
            latitude: lat,
            longitude: lon,
            altitude: alt,
        });
    }
    
    pub fn add_metadata(&mut self, key: &str, value: serde_json::Value) {
        if let serde_json::Value::Object(ref mut map) = self.metadata {
            map.insert(key.to_string(), value);
        }
    }
}
```

## 3. 配置管理

### 3.1 配置文件结构

```yaml
# config/application.yaml
application:
  name: "data-collection-system"
  version: "1.0.0"
  environment: "production"  # development | testing | production

logging:
  level: "info"  # trace | debug | info | warn | error
  format: "json"  # text | json
  output: "file"  # console | file | both
  file_path: "/var/log/data-collector/app.log"
  max_file_size: "100MB"
  max_files: 10

database:
  postgres:
    host: "localhost"
    port: 5432
    database: "data_collection"
    username: "collector"
    password: "${DB_PASSWORD}"
    max_connections: 20
    min_connections: 5
    connection_timeout: 30
    
  influxdb:
    url: "http://localhost:8086"
    database: "metrics"
    username: "collector"
    password: "${INFLUX_PASSWORD}"
    retention_policy: "30d"
    
  minio:
    endpoint: "http://localhost:9000"
    access_key: "${MINIO_ACCESS_KEY}"
    secret_key: "${MINIO_SECRET_KEY}"
    bucket: "data-collection"
    region: "us-east-1"

collectors:
  log_collector:
    enabled: true
    mode: "passive"  # passive | active
    watch_directory: "/data/logs"
    file_pattern: "*.log"
    buffer_size: 8192
    max_file_size: "1GB"
    
  sftp_collector:
    enabled: true
    host: "*************"
    port: 22
    username: "collector"
    private_key_path: "/etc/ssh/collector_key"
    remote_directory: "/logs"
    sync_interval: 60  # seconds
    max_concurrent_downloads: 5
    
  camera_collector:
    enabled: true
    sdk_config:
      library_path: "/usr/lib/camera_sdk.so"
      connection_timeout: 30
      read_timeout: 10
      max_retry_attempts: 3
    devices:
      - device_id: "cam001"
        ip: "*************"
        port: 8000
        username: "admin"
        password: "${CAM001_PASSWORD}"
      - device_id: "cam002"
        ip: "*************"
        port: 8000
        username: "admin"
        password: "${CAM002_PASSWORD}"

processing:
  batch_size: 1000
  max_queue_size: 10000
  worker_threads: 4
  timeout: 30  # seconds
  
monitoring:
  prometheus:
    enabled: true
    listen_address: "0.0.0.0:9090"
    metrics_path: "/metrics"
    
  health_check:
    enabled: true
    listen_address: "0.0.0.0:8080"
    path: "/health"
    
alerts:
  webhook_url: "${ALERT_WEBHOOK_URL}"
  channels:
    - type: "email"
      recipients: ["<EMAIL>"]
    - type: "slack"
      webhook: "${SLACK_WEBHOOK_URL}"
```

### 3.2 配置加载和验证

```rust
use config::{Config, ConfigError, Environment, File};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub application: ApplicationConfig,
    pub logging: LoggingConfig,
    pub database: DatabaseConfig,
    pub collectors: CollectorsConfig,
    pub processing: ProcessingConfig,
    pub monitoring: MonitoringConfig,
    pub alerts: AlertsConfig,
}

impl AppConfig {
    pub fn load() -> Result<Self, ConfigError> {
        let mut config = Config::builder()
            .add_source(File::with_name("config/application"))
            .add_source(Environment::with_prefix("APP").separator("_"))
            .build()?;
            
        let app_config: AppConfig = config.try_deserialize()?;
        app_config.validate()?;
        
        Ok(app_config)
    }
    
    pub fn validate(&self) -> Result<(), ConfigError> {
        // 验证配置的有效性
        if self.collectors.log_collector.enabled && self.collectors.log_collector.watch_directory.is_empty() {
            return Err(ConfigError::Message("Log collector watch directory cannot be empty".to_string()));
        }
        
        if self.collectors.sftp_collector.enabled && self.collectors.sftp_collector.host.is_empty() {
            return Err(ConfigError::Message("SFTP collector host cannot be empty".to_string()));
        }
        
        if self.processing.batch_size == 0 {
            return Err(ConfigError::Message("Processing batch size must be greater than 0".to_string()));
        }
        
        Ok(())
    }
}
```

---

**技术规范附录结束**

*本附录提供了数据采集系统的详细技术实现规范，包括接口定义、数据模型和配置管理等核心技术细节。*
