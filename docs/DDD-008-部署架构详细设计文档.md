# DDD-008 部署架构详细设计文档

**文档编号：** DDD-008  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 运维架构团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md  
**上级设计：** DDD-001-系统总体架构详细设计文档.md

---

## 1. 部署架构概述

### 1.1 设计目的
部署架构详细设计文档定义了数据采集系统的部署拓扑、环境配置、容器化方案、运维监控和自动化部署策略，确保系统的高可用性、可扩展性和可维护性。

### 1.2 需求追溯
本模块对应PRD-001中的以下需求：
- **高可用性要求**: 系统可用性≥99.9%，支持故障自动切换
- **性能要求**: 支持≥1000条/秒数据处理，≤1秒响应时间
- **扩展性要求**: 支持水平扩展，可动态增减节点
- **运维要求**: 支持自动化部署、监控告警、日志收集
- **安全要求**: 网络隔离、访问控制、数据加密

### 1.3 部署原则
- **微服务化**: 模块独立部署，服务解耦
- **容器化**: 使用Docker容器化部署
- **自动化**: CI/CD自动化部署流水线
- **监控化**: 全方位监控和告警
- **标准化**: 统一的部署规范和配置管理

## 2. 部署架构设计

### 2.1 整体部署架构

```mermaid
graph TB
    subgraph "外部网络"
        INTERNET[互联网]
        VPN[VPN接入]
    end

    subgraph "DMZ区域"
        LB[负载均衡器<br/>Nginx/HAProxy]
        WAF[Web应用防火墙]
    end

    subgraph "应用服务区"
        subgraph "Kubernetes集群"
            subgraph "Web层"
                WEB1[Web服务 Pod 1]
                WEB2[Web服务 Pod 2]
                WEB3[Web服务 Pod 3]
            end
            
            subgraph "API层"
                API1[API服务 Pod 1]
                API2[API服务 Pod 2]
                API3[API服务 Pod 3]
            end
            
            subgraph "业务层"
                HCS1[HCS主服务 Pod 1]
                HCS2[HCS主服务 Pod 2]
                COLLECTOR[数据采集服务]
                PROCESSOR[数据处理服务]
                TRANSPORT[数据传输服务]
            end
        end
        
        subgraph "中间件层"
            REDIS_CLUSTER[Redis集群]
            MQ[消息队列<br/>RabbitMQ]
        end
    end

    subgraph "数据服务区"
        subgraph "数据库集群"
            PG_MASTER[PostgreSQL主库]
            PG_SLAVE1[PostgreSQL从库1]
            PG_SLAVE2[PostgreSQL从库2]
        end
        
        subgraph "存储服务"
            MINIO[MinIO对象存储]
            NFS[NFS共享存储]
        end
        
        subgraph "搜索服务"
            ES_CLUSTER[Elasticsearch集群]
        end
    end

    subgraph "监控服务区"
        PROMETHEUS[Prometheus]
        GRAFANA[Grafana]
        ALERTMANAGER[AlertManager]
        JAEGER[Jaeger链路追踪]
    end

    subgraph "日志服务区"
        FLUENTD[Fluentd]
        ELASTICSEARCH[Elasticsearch]
        KIBANA[Kibana]
    end

    subgraph "基础设施"
        ETCD[etcd集群]
        DNS[内部DNS]
        NTP[时间同步服务]
    end

    INTERNET --> WAF
    VPN --> WAF
    WAF --> LB
    
    LB --> WEB1
    LB --> WEB2
    LB --> WEB3
    
    WEB1 --> API1
    WEB2 --> API2
    WEB3 --> API3
    
    API1 --> HCS1
    API2 --> HCS2
    API3 --> HCS1
    
    HCS1 --> COLLECTOR
    HCS2 --> PROCESSOR
    HCS1 --> TRANSPORT
    
    HCS1 --> REDIS_CLUSTER
    HCS2 --> REDIS_CLUSTER
    HCS1 --> MQ
    HCS2 --> MQ
    
    HCS1 --> PG_MASTER
    HCS2 --> PG_SLAVE1
    API1 --> PG_SLAVE2
    
    COLLECTOR --> MINIO
    PROCESSOR --> NFS
    
    API1 --> ES_CLUSTER
    API2 --> ES_CLUSTER
    
    HCS1 --> PROMETHEUS
    HCS2 --> PROMETHEUS
    PROMETHEUS --> GRAFANA
    PROMETHEUS --> ALERTMANAGER
    
    HCS1 --> FLUENTD
    HCS2 --> FLUENTD
    FLUENTD --> ELASTICSEARCH
    ELASTICSEARCH --> KIBANA
```

### 2.2 网络架构设计

```mermaid
graph TB
    subgraph "网络分层架构"
        subgraph "接入层 (10.0.1.0/24)"
            ACCESS_SWITCH[接入交换机]
            FIREWALL[防火墙]
        end
        
        subgraph "DMZ区 (10.0.10.0/24)"
            DMZ_LB[负载均衡器<br/>10.0.10.10]
            DMZ_WAF[WAF<br/>10.0.10.20]
        end
        
        subgraph "应用区 (10.0.20.0/24)"
            APP_WEB[Web服务<br/>10.0.20.10-12]
            APP_API[API服务<br/>10.0.20.20-22]
            APP_HCS[HCS服务<br/>10.0.20.30-32]
        end
        
        subgraph "数据区 (10.0.30.0/24)"
            DATA_PG[PostgreSQL<br/>10.0.30.10-12]
            DATA_REDIS[Redis<br/>10.0.30.20-22]
            DATA_ES[Elasticsearch<br/>10.0.30.30-32]
        end
        
        subgraph "监控区 (10.0.40.0/24)"
            MON_PROM[Prometheus<br/>10.0.40.10]
            MON_GRAF[Grafana<br/>10.0.40.20]
            MON_ALERT[AlertManager<br/>10.0.40.30]
        end
        
        subgraph "管理区 (10.0.50.0/24)"
            MGMT_JUMP[跳板机<br/>10.0.50.10]
            MGMT_DEPLOY[部署服务器<br/>10.0.50.20]
            MGMT_BACKUP[备份服务器<br/>10.0.50.30]
        end
    end

    ACCESS_SWITCH --> FIREWALL
    FIREWALL --> DMZ_LB
    FIREWALL --> DMZ_WAF
    
    DMZ_LB --> APP_WEB
    DMZ_WAF --> APP_API
    
    APP_WEB --> APP_API
    APP_API --> APP_HCS
    
    APP_HCS --> DATA_PG
    APP_HCS --> DATA_REDIS
    APP_API --> DATA_ES
    
    APP_HCS --> MON_PROM
    MON_PROM --> MON_GRAF
    MON_PROM --> MON_ALERT
    
    MGMT_JUMP --> APP_HCS
    MGMT_DEPLOY --> APP_HCS
    MGMT_BACKUP --> DATA_PG
```

## 3. 容器化部署设计

### 3.1 Docker容器设计

#### 3.1.1 HCS主服务容器

```dockerfile
# Dockerfile for HCS Main Service
FROM rust:1.70-slim as builder

WORKDIR /app
COPY . .

# 安装依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 构建应用
RUN cargo build --release

# 运行时镜像
FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# 复制二进制文件
COPY --from=builder /app/target/release/hcs /app/hcs
COPY --from=builder /app/config /app/config

# 创建非root用户
RUN useradd -r -s /bin/false hcs && \
    chown -R hcs:hcs /app

USER hcs

EXPOSE 8080
EXPOSE 9090

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:9090/health || exit 1

CMD ["./hcs"]
```

#### 3.1.2 API服务容器

```dockerfile
# Dockerfile for API Service
FROM rust:1.70-slim as builder

WORKDIR /app
COPY . .

RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

RUN cargo build --release --bin api-server

FROM debian:bookworm-slim

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    libpq5 \
    curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY --from=builder /app/target/release/api-server /app/api-server
COPY --from=builder /app/config /app/config

RUN useradd -r -s /bin/false apiuser && \
    chown -R apiuser:apiuser /app

USER apiuser

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

CMD ["./api-server"]
```

### 3.2 Kubernetes部署配置

#### 3.2.1 HCS主服务部署

```yaml
# k8s/hcs-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hcs-main
  namespace: hcs-system
  labels:
    app: hcs-main
    version: v1.0.0
spec:
  replicas: 2
  selector:
    matchLabels:
      app: hcs-main
  template:
    metadata:
      labels:
        app: hcs-main
        version: v1.0.0
    spec:
      containers:
      - name: hcs-main
        image: hcs/hcs-main:v1.0.0
        ports:
        - containerPort: 8080
          name: http
        - containerPort: 9090
          name: metrics
        env:
        - name: RUST_LOG
          value: "info"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: hcs-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: hcs-secrets
              key: redis-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        - name: data-volume
          mountPath: /app/data
      volumes:
      - name: config-volume
        configMap:
          name: hcs-config
      - name: data-volume
        persistentVolumeClaim:
          claimName: hcs-data-pvc
      nodeSelector:
        node-type: application
      tolerations:
      - key: "application"
        operator: "Equal"
        value: "true"
        effect: "NoSchedule"

---
apiVersion: v1
kind: Service
metadata:
  name: hcs-main-service
  namespace: hcs-system
  labels:
    app: hcs-main
spec:
  selector:
    app: hcs-main
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: hcs-config
  namespace: hcs-system
data:
  app.yaml: |
    server:
      host: "0.0.0.0"
      port: 8080
      metrics_port: 9090
    
    database:
      max_connections: 20
      connection_timeout: 30
    
    redis:
      pool_size: 10
      timeout: 5
    
    logging:
      level: "info"
      format: "json"

---
apiVersion: v1
kind: Secret
metadata:
  name: hcs-secrets
  namespace: hcs-system
type: Opaque
data:
  database-url: cG9zdGdyZXNxbDovL3VzZXI6cGFzc3dvcmRAaG9zdDpwb3J0L2RiX25hbWU=
  redis-url: cmVkaXM6Ly86cGFzc3dvcmRAaG9zdDpwb3J0L2Ri
```

#### 3.2.2 API服务部署

```yaml
# k8s/api-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: hcs-api
  namespace: hcs-system
  labels:
    app: hcs-api
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: hcs-api
  template:
    metadata:
      labels:
        app: hcs-api
        version: v1.0.0
    spec:
      containers:
      - name: hcs-api
        image: hcs/hcs-api:v1.0.0
        ports:
        - containerPort: 3000
          name: http
        env:
        - name: RUST_LOG
          value: "info"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: hcs-secrets
              key: database-url
        - name: REDIS_URL
          valueFrom:
            secretKeyRef:
              name: hcs-secrets
              key: redis-url
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: hcs-api-service
  namespace: hcs-system
  labels:
    app: hcs-api
spec:
  selector:
    app: hcs-api
  ports:
  - name: http
    port: 3000
    targetPort: 3000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: hcs-api-ingress
  namespace: hcs-system
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.hcs.example.com
    secretName: hcs-api-tls
  rules:
  - host: api.hcs.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: hcs-api-service
            port:
              number: 3000
```

#### 3.2.3 数据库部署

```yaml
# k8s/postgresql-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-primary
  namespace: hcs-system
  labels:
    app: postgresql
    role: primary
spec:
  serviceName: postgresql-primary-service
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
      role: primary
  template:
    metadata:
      labels:
        app: postgresql
        role: primary
    spec:
      containers:
      - name: postgresql
        image: postgres:15
        ports:
        - containerPort: 5432
          name: postgresql
        env:
        - name: POSTGRES_DB
          value: "hcs_db"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: password
        - name: POSTGRES_REPLICATION_USER
          value: "replicator"
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-secrets
              key: replication-password
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: postgresql-data
          mountPath: /var/lib/postgresql/data
        - name: postgresql-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $POSTGRES_USER -d $POSTGRES_DB
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - pg_isready -U $POSTGRES_USER -d $POSTGRES_DB
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: postgresql-config
        configMap:
          name: postgresql-config
  volumeClaimTemplates:
  - metadata:
      name: postgresql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-primary-service
  namespace: hcs-system
  labels:
    app: postgresql
    role: primary
spec:
  selector:
    app: postgresql
    role: primary
  ports:
  - name: postgresql
    port: 5432
    targetPort: 5432
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-config
  namespace: hcs-system
data:
  postgresql.conf: |
    # PostgreSQL configuration
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    shared_buffers = 256MB
    effective_cache_size = 1GB
    work_mem = 4MB
    maintenance_work_mem = 64MB

    # WAL settings
    wal_level = replica
    max_wal_senders = 3
    wal_keep_segments = 100
    archive_mode = on
    archive_command = 'cp %p /var/lib/postgresql/archive/%f'

    # Logging
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'pg_log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_statement = 'all'
    log_min_duration_statement = 1000

---
apiVersion: v1
kind: Secret
metadata:
  name: postgresql-secrets
  namespace: hcs-system
type: Opaque
data:
  username: aGNzX3VzZXI=  # hcs_user
  password: c2VjdXJlX3Bhc3N3b3JkX2hlcmU=  # secure_password_here
  replication-password: cmVwbGljYXRpb25fcGFzc3dvcmQ=  # replication_password
```

#### 3.2.4 Redis集群部署

```yaml
# k8s/redis-deployment.yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: hcs-system
  labels:
    app: redis
spec:
  serviceName: redis-cluster-service
  replicas: 6
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
          name: client
        - containerPort: 16379
          name: gossip
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - /data/nodes.conf
        - --cluster-node-timeout
        - "5000"
        - --appendonly
        - "yes"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        livenessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          exec:
            command:
            - redis-cli
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster-service
  namespace: hcs-system
  labels:
    app: redis
spec:
  selector:
    app: redis
  ports:
  - name: client
    port: 6379
    targetPort: 6379
  - name: gossip
    port: 16379
    targetPort: 16379
  clusterIP: None

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: hcs-system
data:
  redis.conf: |
    # Redis configuration
    bind 0.0.0.0
    port 6379
    protected-mode no

    # Memory management
    maxmemory 256mb
    maxmemory-policy allkeys-lru

    # Persistence
    save 900 1
    save 300 10
    save 60 10000

    # Logging
    loglevel notice
    logfile ""
```

## 4. 监控和日志部署

### 4.1 Prometheus监控部署

```yaml
# k8s/monitoring/prometheus-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
  labels:
    app: prometheus
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      serviceAccountName: prometheus
      containers:
      - name: prometheus
        image: prom/prometheus:v2.40.0
        ports:
        - containerPort: 9090
          name: web
        args:
        - --config.file=/etc/prometheus/prometheus.yml
        - --storage.tsdb.path=/prometheus/
        - --web.console.libraries=/etc/prometheus/console_libraries
        - --web.console.templates=/etc/prometheus/consoles
        - --storage.tsdb.retention.time=30d
        - --web.enable-lifecycle
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus
        - name: prometheus-data
          mountPath: /prometheus
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-data
        persistentVolumeClaim:
          claimName: prometheus-data-pvc

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
    - "/etc/prometheus/rules/*.yml"

    alerting:
      alertmanagers:
      - static_configs:
        - targets:
          - alertmanager:9093

    scrape_configs:
    - job_name: 'prometheus'
      static_configs:
      - targets: ['localhost:9090']

    - job_name: 'hcs-main'
      kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
          - hcs-system
      relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: hcs-main-service
      - source_labels: [__meta_kubernetes_endpoint_port_name]
        action: keep
        regex: metrics

    - job_name: 'hcs-api'
      kubernetes_sd_configs:
      - role: endpoints
        namespaces:
          names:
          - hcs-system
      relabel_configs:
      - source_labels: [__meta_kubernetes_service_name]
        action: keep
        regex: hcs-api-service

    - job_name: 'postgresql'
      static_configs:
      - targets: ['postgresql-exporter:9187']

    - job_name: 'redis'
      static_configs:
      - targets: ['redis-exporter:9121']

    - job_name: 'node-exporter'
      kubernetes_sd_configs:
      - role: node
      relabel_configs:
      - source_labels: [__address__]
        regex: '(.*):10250'
        target_label: __address__
        replacement: '${1}:9100'

  alerts.yml: |
    groups:
    - name: hcs-alerts
      rules:
      - alert: HCSServiceDown
        expr: up{job=~"hcs-.*"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "HCS service {{ $labels.job }} is down"
          description: "HCS service {{ $labels.job }} has been down for more than 1 minute."

      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second."

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 80% for more than 5 minutes."

      - alert: DatabaseConnectionsHigh
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High database connections"
          description: "Database connections are above 80% of max connections."
```

### 4.2 Grafana部署

```yaml
# k8s/monitoring/grafana-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
  labels:
    app: grafana
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:9.0.0
        ports:
        - containerPort: 3000
          name: web
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GF_INSTALL_PLUGINS
          value: "grafana-piechart-panel,grafana-worldmap-panel"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        volumeMounts:
        - name: grafana-data
          mountPath: /var/lib/grafana
        - name: grafana-config
          mountPath: /etc/grafana
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: grafana-data
        persistentVolumeClaim:
          claimName: grafana-data-pvc
      - name: grafana-config
        configMap:
          name: grafana-config

---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: monitoring
  labels:
    app: grafana
spec:
  selector:
    app: grafana
  ports:
  - name: web
    port: 3000
    targetPort: 3000
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: monitoring
data:
  grafana.ini: |
    [server]
    http_port = 3000
    domain = grafana.hcs.example.com

    [database]
    type = sqlite3
    path = /var/lib/grafana/grafana.db

    [auth]
    disable_login_form = false

    [security]
    admin_user = admin

    [dashboards]
    default_home_dashboard_path = /var/lib/grafana/dashboards/hcs-overview.json

    [alerting]
    enabled = true
    execute_alerts = true
```

### 4.3 日志收集部署

```yaml
# k8s/logging/fluentd-daemonset.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluentd
  namespace: logging
  labels:
    app: fluentd
spec:
  selector:
    matchLabels:
      app: fluentd
  template:
    metadata:
      labels:
        app: fluentd
    spec:
      serviceAccountName: fluentd
      containers:
      - name: fluentd
        image: fluent/fluentd-kubernetes-daemonset:v1.15-debian-elasticsearch7-1
        env:
        - name: FLUENT_ELASTICSEARCH_HOST
          value: "elasticsearch.logging.svc.cluster.local"
        - name: FLUENT_ELASTICSEARCH_PORT
          value: "9200"
        - name: FLUENT_ELASTICSEARCH_SCHEME
          value: "http"
        - name: FLUENT_UID
          value: "0"
        resources:
          requests:
            memory: "200Mi"
            cpu: "100m"
          limits:
            memory: "400Mi"
            cpu: "200m"
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: fluentd-config
          mountPath: /fluentd/etc
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: fluentd-config
        configMap:
          name: fluentd-config

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: logging
data:
  fluent.conf: |
    <source>
      @type tail
      @id in_tail_container_logs
      path /var/log/containers/*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      read_from_head true
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
      </parse>
    </source>

    <filter kubernetes.**>
      @type kubernetes_metadata
      @id filter_kube_metadata
    </filter>

    <filter kubernetes.**>
      @type grep
      <regexp>
        key $.kubernetes.namespace_name
        pattern ^(hcs-system|monitoring|logging)$
      </regexp>
    </filter>

    <match kubernetes.**>
      @type elasticsearch
      @id out_es
      host "#{ENV['FLUENT_ELASTICSEARCH_HOST']}"
      port "#{ENV['FLUENT_ELASTICSEARCH_PORT']}"
      scheme "#{ENV['FLUENT_ELASTICSEARCH_SCHEME']}"
      index_name fluentd-kubernetes
      type_name _doc
      <buffer>
        @type file
        path /var/log/fluentd-buffers/kubernetes.system.buffer
        flush_mode interval
        retry_type exponential_backoff
        flush_thread_count 2
        flush_interval 5s
        retry_forever
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 8
        overflow_action block
      </buffer>
    </match>
```

## 5. CI/CD自动化部署

### 5.1 GitLab CI/CD配置

```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - security
  - deploy-dev
  - deploy-staging
  - deploy-prod

variables:
  DOCKER_REGISTRY: "registry.hcs.example.com"
  DOCKER_IMAGE_NAME: "hcs/hcs-main"
  KUBERNETES_NAMESPACE_DEV: "hcs-dev"
  KUBERNETES_NAMESPACE_STAGING: "hcs-staging"
  KUBERNETES_NAMESPACE_PROD: "hcs-system"

# 测试阶段
test:
  stage: test
  image: rust:1.70
  before_script:
    - apt-get update && apt-get install -y libpq-dev
    - cargo --version
  script:
    - cargo test --verbose
    - cargo clippy -- -D warnings
    - cargo fmt -- --check
  coverage: '/^\d+\.\d+% coverage/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
  only:
    - merge_requests
    - main
    - develop

# 构建阶段
build:
  stage: build
  image: docker:20.10.16
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA .
    - docker build -t $DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:latest .
    - docker push $DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:latest
  only:
    - main
    - develop
    - tags

# 安全扫描
security_scan:
  stage: security
  image: aquasec/trivy:latest
  script:
    - trivy image --exit-code 0 --severity HIGH,CRITICAL $DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA
  allow_failure: true
  only:
    - main
    - develop

# 开发环境部署
deploy_dev:
  stage: deploy-dev
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_DEV
  script:
    - envsubst < k8s/hcs-deployment.yaml | kubectl apply -f -
    - kubectl set image deployment/hcs-main hcs-main=$DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_DEV
    - kubectl rollout status deployment/hcs-main -n $KUBERNETES_NAMESPACE_DEV --timeout=300s
  environment:
    name: development
    url: https://dev.hcs.example.com
  only:
    - develop

# 预发布环境部署
deploy_staging:
  stage: deploy-staging
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_STAGING
  script:
    - envsubst < k8s/hcs-deployment.yaml | kubectl apply -f -
    - kubectl set image deployment/hcs-main hcs-main=$DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_STAGING
    - kubectl rollout status deployment/hcs-main -n $KUBERNETES_NAMESPACE_STAGING --timeout=300s
  environment:
    name: staging
    url: https://staging.hcs.example.com
  when: manual
  only:
    - main

# 生产环境部署
deploy_prod:
  stage: deploy-prod
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_PROD
  script:
    - envsubst < k8s/hcs-deployment.yaml | kubectl apply -f -
    - kubectl set image deployment/hcs-main hcs-main=$DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_PROD
    - kubectl rollout status deployment/hcs-main -n $KUBERNETES_NAMESPACE_PROD --timeout=600s
    - kubectl get pods -n $KUBERNETES_NAMESPACE_PROD
  environment:
    name: production
    url: https://hcs.example.com
  when: manual
  only:
    - tags
```

### 5.2 Helm Chart配置

```yaml
# helm/hcs/Chart.yaml
apiVersion: v2
name: hcs
description: High-speed Toll Collection System
type: application
version: 1.0.0
appVersion: "1.0.0"
keywords:
  - hcs
  - toll-collection
  - data-processing
home: https://hcs.example.com
sources:
  - https://github.com/company/hcs
maintainers:
  - name: HCS Team
    email: <EMAIL>

---
# helm/hcs/values.yaml
# Default values for hcs
replicaCount: 2

image:
  repository: hcs/hcs-main
  pullPolicy: IfNotPresent
  tag: "latest"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: ClusterIP
  port: 8080
  metricsPort: 9090

ingress:
  enabled: true
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
  hosts:
    - host: hcs.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: hcs-tls
      hosts:
        - hcs.example.com

resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 500m
    memory: 512Mi

autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 70
  targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

# Database configuration
database:
  host: postgresql-primary-service
  port: 5432
  name: hcs_db
  username: hcs_user
  existingSecret: hcs-secrets
  secretKey: database-password

# Redis configuration
redis:
  host: redis-cluster-service
  port: 6379
  existingSecret: hcs-secrets
  secretKey: redis-password

# Application configuration
config:
  logLevel: info
  metricsEnabled: true
  tracingEnabled: true

# Monitoring
monitoring:
  enabled: true
  serviceMonitor:
    enabled: true
    interval: 30s
    path: /metrics
    port: metrics

# Persistence
persistence:
  enabled: true
  storageClass: "fast-ssd"
  accessMode: ReadWriteOnce
  size: 10Gi
```

### 5.3 部署脚本

```bash
#!/bin/bash
# deploy.sh - 部署脚本

set -e

# 配置变量
ENVIRONMENT=${1:-dev}
NAMESPACE="hcs-${ENVIRONMENT}"
HELM_RELEASE="hcs-${ENVIRONMENT}"
CHART_PATH="./helm/hcs"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "Checking dependencies..."

    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed"
        exit 1
    fi

    if ! command -v helm &> /dev/null; then
        log_error "helm is not installed"
        exit 1
    fi

    # 检查Kubernetes连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Cannot connect to Kubernetes cluster"
        exit 1
    fi

    log_info "Dependencies check passed"
}

# 创建命名空间
create_namespace() {
    log_info "Creating namespace: ${NAMESPACE}"

    kubectl create namespace ${NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -

    # 添加标签
    kubectl label namespace ${NAMESPACE} environment=${ENVIRONMENT} --overwrite
}

# 部署基础设施
deploy_infrastructure() {
    log_info "Deploying infrastructure components..."

    # 部署PostgreSQL
    if [ "${ENVIRONMENT}" != "prod" ]; then
        log_info "Deploying PostgreSQL for ${ENVIRONMENT}"
        kubectl apply -f k8s/postgresql-deployment.yaml -n ${NAMESPACE}
    fi

    # 部署Redis
    log_info "Deploying Redis cluster"
    kubectl apply -f k8s/redis-deployment.yaml -n ${NAMESPACE}

    # 等待基础设施就绪
    log_info "Waiting for infrastructure to be ready..."
    kubectl wait --for=condition=ready pod -l app=postgresql --timeout=300s -n ${NAMESPACE} || true
    kubectl wait --for=condition=ready pod -l app=redis --timeout=300s -n ${NAMESPACE} || true
}

# 部署应用
deploy_application() {
    log_info "Deploying HCS application..."

    # 使用Helm部署
    helm upgrade --install ${HELM_RELEASE} ${CHART_PATH} \
        --namespace ${NAMESPACE} \
        --values ${CHART_PATH}/values-${ENVIRONMENT}.yaml \
        --set image.tag=${IMAGE_TAG:-latest} \
        --wait --timeout=600s

    log_info "Application deployed successfully"
}

# 验证部署
verify_deployment() {
    log_info "Verifying deployment..."

    # 检查Pod状态
    kubectl get pods -n ${NAMESPACE}

    # 检查服务状态
    kubectl get services -n ${NAMESPACE}

    # 检查Ingress状态
    kubectl get ingress -n ${NAMESPACE}

    # 健康检查
    log_info "Performing health check..."

    # 等待所有Pod就绪
    kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=hcs --timeout=300s -n ${NAMESPACE}

    # 检查应用健康状态
    if kubectl get ingress hcs-ingress -n ${NAMESPACE} &> /dev/null; then
        INGRESS_HOST=$(kubectl get ingress hcs-ingress -n ${NAMESPACE} -o jsonpath='{.spec.rules[0].host}')
        log_info "Health check URL: https://${INGRESS_HOST}/health"
    fi

    log_info "Deployment verification completed"
}

# 回滚部署
rollback_deployment() {
    log_warn "Rolling back deployment..."

    helm rollback ${HELM_RELEASE} --namespace ${NAMESPACE}

    log_info "Rollback completed"
}

# 清理部署
cleanup_deployment() {
    log_warn "Cleaning up deployment..."

    helm uninstall ${HELM_RELEASE} --namespace ${NAMESPACE}

    # 可选：删除PVC
    read -p "Do you want to delete persistent volumes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        kubectl delete pvc --all -n ${NAMESPACE}
    fi

    log_info "Cleanup completed"
}

# 主函数
main() {
    case "${2:-deploy}" in
        "deploy")
            check_dependencies
            create_namespace
            deploy_infrastructure
            deploy_application
            verify_deployment
            ;;
        "rollback")
            rollback_deployment
            ;;
        "cleanup")
            cleanup_deployment
            ;;
        *)
            echo "Usage: $0 <environment> [deploy|rollback|cleanup]"
            echo "Environments: dev, staging, prod"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
```

## 6. 运维管理

### 6.1 系统监控策略

#### 6.1.1 监控指标体系

```yaml
# 监控指标配置
monitoring_metrics:
  # 系统级指标
  system:
    - cpu_usage_percent
    - memory_usage_percent
    - disk_usage_percent
    - network_io_bytes
    - load_average

  # 应用级指标
  application:
    - http_requests_total
    - http_request_duration_seconds
    - active_connections
    - queue_size
    - error_rate

  # 业务级指标
  business:
    - transactions_per_second
    - vehicles_processed_per_minute
    - data_quality_score
    - processing_latency

  # 数据库指标
  database:
    - pg_up
    - pg_connections_active
    - pg_connections_max
    - pg_query_duration
    - pg_cache_hit_ratio

  # Redis指标
  redis:
    - redis_up
    - redis_connected_clients
    - redis_memory_usage_bytes
    - redis_commands_processed_total
```

#### 6.1.2 告警规则配置

```yaml
# 告警规则配置
alert_rules:
  critical:
    - name: "ServiceDown"
      condition: "up == 0"
      duration: "1m"
      action: "immediate_notification"

    - name: "HighErrorRate"
      condition: "rate(http_requests_total{status=~'5..'}[5m]) > 0.1"
      duration: "2m"
      action: "immediate_notification"

    - name: "DatabaseDown"
      condition: "pg_up == 0"
      duration: "30s"
      action: "immediate_notification"

  warning:
    - name: "HighCPUUsage"
      condition: "cpu_usage_percent > 80"
      duration: "5m"
      action: "notification"

    - name: "HighMemoryUsage"
      condition: "memory_usage_percent > 85"
      duration: "5m"
      action: "notification"

    - name: "DiskSpaceLow"
      condition: "disk_usage_percent > 90"
      duration: "10m"
      action: "notification"

  info:
    - name: "HighLatency"
      condition: "http_request_duration_seconds{quantile='0.95'} > 1"
      duration: "10m"
      action: "log_only"
```

### 6.2 日志管理策略

#### 6.2.1 日志分类和保留策略

```yaml
# 日志管理配置
log_management:
  categories:
    application:
      retention_days: 30
      log_level: "info"
      format: "json"
      rotation: "daily"

    access:
      retention_days: 90
      format: "combined"
      rotation: "daily"
      compression: true

    error:
      retention_days: 180
      log_level: "error"
      format: "json"
      alert_on_error: true

    audit:
      retention_days: 2555  # 7 years
      format: "json"
      immutable: true
      backup_enabled: true

    security:
      retention_days: 365
      format: "json"
      real_time_analysis: true
      siem_integration: true

  storage:
    hot_tier:
      duration_days: 7
      storage_class: "ssd"
      search_enabled: true

    warm_tier:
      duration_days: 30
      storage_class: "sata"
      search_enabled: true

    cold_tier:
      duration_days: 365
      storage_class: "archive"
      search_enabled: false
```

### 6.3 备份和恢复策略

#### 6.3.1 备份策略配置

```bash
#!/bin/bash
# backup-strategy.sh - 备份策略脚本

# 数据库备份策略
database_backup() {
    local backup_type=$1
    local retention_days=$2

    case $backup_type in
        "full")
            # 全量备份
            pg_dump -h $DB_HOST -U $DB_USER -d $DB_NAME \
                --format=custom \
                --compress=9 \
                --file=/backup/db/full_$(date +%Y%m%d_%H%M%S).dump

            # 保留策略：全量备份保留30天
            find /backup/db -name "full_*.dump" -mtime +30 -delete
            ;;

        "incremental")
            # 增量备份（WAL归档）
            rsync -av /var/lib/postgresql/15/main/pg_wal/ /backup/db/wal/

            # 保留策略：WAL文件保留7天
            find /backup/db/wal -name "*.wal" -mtime +7 -delete
            ;;
    esac
}

# 应用数据备份
application_backup() {
    # 配置文件备份
    tar -czf /backup/app/config_$(date +%Y%m%d_%H%M%S).tar.gz \
        /app/config \
        /etc/kubernetes/manifests

    # 持久化数据备份
    kubectl get pvc -n hcs-system -o yaml > /backup/app/pvc_$(date +%Y%m%d_%H%M%S).yaml

    # 保留策略：应用备份保留14天
    find /backup/app -name "*.tar.gz" -mtime +14 -delete
    find /backup/app -name "*.yaml" -mtime +14 -delete
}

# 系统备份
system_backup() {
    # etcd备份
    ETCDCTL_API=3 etcdctl snapshot save /backup/system/etcd_$(date +%Y%m%d_%H%M%S).db \
        --endpoints=https://127.0.0.1:2379 \
        --cacert=/etc/kubernetes/pki/etcd/ca.crt \
        --cert=/etc/kubernetes/pki/etcd/server.crt \
        --key=/etc/kubernetes/pki/etcd/server.key

    # 保留策略：系统备份保留7天
    find /backup/system -name "etcd_*.db" -mtime +7 -delete
}

# 远程备份同步
remote_sync() {
    # 同步到远程存储
    aws s3 sync /backup/ s3://hcs-backup-bucket/$(hostname)/ \
        --delete \
        --storage-class STANDARD_IA

    # 同步到备份中心
    rsync -av --delete /backup/ backup-server:/backup/$(hostname)/
}

# 执行备份任务
case "${1:-all}" in
    "database")
        database_backup "full" 30
        database_backup "incremental" 7
        ;;
    "application")
        application_backup
        ;;
    "system")
        system_backup
        ;;
    "all")
        database_backup "full" 30
        database_backup "incremental" 7
        application_backup
        system_backup
        remote_sync
        ;;
    *)
        echo "Usage: $0 [database|application|system|all]"
        exit 1
        ;;
esac
```

### 6.4 性能调优策略

#### 6.4.1 系统性能优化

```yaml
# 性能调优配置
performance_tuning:
  kubernetes:
    node_configuration:
      cpu_policy: "static"
      memory_policy: "strict"
      hugepages: "2Mi:1024"
      kernel_parameters:
        - "vm.swappiness=1"
        - "net.core.somaxconn=65535"
        - "net.ipv4.tcp_max_syn_backlog=65535"

    pod_configuration:
      resource_requests:
        cpu: "500m"
        memory: "512Mi"
      resource_limits:
        cpu: "2000m"
        memory: "2Gi"
      quality_of_service: "Guaranteed"

  database:
    postgresql_tuning:
      shared_buffers: "25% of RAM"
      effective_cache_size: "75% of RAM"
      work_mem: "4MB"
      maintenance_work_mem: "256MB"
      max_connections: 200
      checkpoint_completion_target: 0.9
      wal_buffers: "16MB"
      random_page_cost: 1.1  # For SSD

    connection_pooling:
      pgbouncer:
        pool_mode: "transaction"
        max_client_conn: 1000
        default_pool_size: 25
        reserve_pool_size: 5

  redis:
    memory_optimization:
      maxmemory_policy: "allkeys-lru"
      hash_max_ziplist_entries: 512
      hash_max_ziplist_value: 64
      list_max_ziplist_size: -2
      set_max_intset_entries: 512

    persistence_optimization:
      save_policy: "900 1 300 10 60 10000"
      rdbcompression: true
      rdbchecksum: true

  application:
    rust_optimization:
      cargo_profile:
        release:
          opt_level: 3
          lto: true
          codegen_units: 1
          panic: "abort"

      runtime_configuration:
        tokio_worker_threads: "auto"
        max_blocking_threads: 512
        thread_stack_size: "2MB"
```

## 7. 安全配置

### 7.1 网络安全

```yaml
# 网络安全策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: hcs-network-policy
  namespace: hcs-system
spec:
  podSelector:
    matchLabels:
      app: hcs-main
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: hcs-system
    - podSelector:
        matchLabels:
          app: hcs-api
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: hcs-system
    ports:
    - protocol: TCP
      port: 5432  # PostgreSQL
    - protocol: TCP
      port: 6379  # Redis
  - to: []
    ports:
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
```

### 7.2 Pod安全策略

```yaml
# Pod安全策略
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: hcs-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## 8. 总结

部署架构详细设计文档为数据采集系统提供了完整的部署和运维解决方案。本设计文档详细描述了：

1. **完整的部署架构**: 从网络架构到应用部署的全方位设计
2. **容器化部署方案**: Docker容器化和Kubernetes编排的完整配置
3. **自动化CI/CD流水线**: GitLab CI/CD和Helm Chart的自动化部署
4. **全面的监控体系**: Prometheus、Grafana和日志收集的监控方案
5. **可靠的运维策略**: 备份恢复、性能调优和故障处理策略
6. **严密的安全配置**: 网络安全、访问控制和数据保护措施

该设计确保了系统的高可用性、高性能、高安全性和高可维护性，为数据采集系统提供了稳定可靠的部署和运维基础。通过容器化、自动化和标准化的部署方案，能够支持快速部署、弹性扩展和高效运维，满足企业级应用的部署和运维需求。
