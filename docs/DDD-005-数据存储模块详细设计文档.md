# DDD-005 数据存储模块详细设计文档

**文档编号：** DDD-005  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 数据存储团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md  
**上级设计：** DDD-001-系统总体架构详细设计文档.md

---

## 1. 模块概述

### 1.1 模块目的
数据存储模块负责将处理后的数据进行持久化存储，提供高效的数据存储、检索和管理功能，支持本地文件存储和数据库存储多种方式，确保数据的安全性和可用性。

### 1.2 需求追溯
本模块对应PRD-001中的以下需求：
- **数据存储和管理**: 提供统一的数据存储方案
- **存储容量要求**: 支持≥1TB数据存储，可扩展
- **数据备份**: 支持实时数据备份和恢复
- **数据完整性**: 数据丢失率≤0.01%
- **查询性能**: 支持高效的数据检索和查询

### 1.3 模块职责
- **本地文件存储**: 高性能本地文件系统存储
- **数据库存储**: 结构化数据的关系型数据库存储
- **存储策略管理**: 数据分区、压缩、归档策略
- **数据生命周期管理**: 数据保留、清理、迁移
- **备份和恢复**: 数据备份、恢复和灾难恢复
- **存储监控**: 存储空间监控、性能统计

## 2. 模块架构设计

### 2.1 存储架构图

```mermaid
graph TB
    subgraph "数据存储模块 (Storage Crate)"
        subgraph "存储接口层"
            SI1[DataStorage Trait<br/>存储接口定义]
            SI2[StorageManager<br/>存储管理器]
            SI3[StorageRouter<br/>存储路由器]
        end
        
        subgraph "本地文件存储层"
            FS1[FileStorage<br/>文件存储器]
            FS2[FileManager<br/>文件管理器]
            FS3[FileIndexer<br/>文件索引器]
            FS4[CompressionEngine<br/>压缩引擎]
        end
        
        subgraph "数据库存储层"
            DB1[DatabaseStorage<br/>数据库存储器]
            DB2[ConnectionPool<br/>连接池]
            DB3[QueryBuilder<br/>查询构建器]
            DB4[SchemaManager<br/>模式管理器]
        end
        
        subgraph "存储策略层"
            ST1[PartitionStrategy<br/>分区策略]
            ST2[RetentionPolicy<br/>保留策略]
            ST3[ArchiveManager<br/>归档管理器]
            ST4[BackupManager<br/>备份管理器]
        end
        
        subgraph "监控统计层"
            MS1[StorageMetrics<br/>存储指标]
            MS2[HealthMonitor<br/>健康监控]
            MS3[SpaceMonitor<br/>空间监控]
            MS4[PerformanceTracker<br/>性能跟踪]
        end
    end
    
    subgraph "外部存储"
        EXT1[本地文件系统]
        EXT2[PostgreSQL数据库]
        EXT3[备份存储]
        EXT4[归档存储]
    end

    SI2 --> SI3
    SI3 --> FS1
    SI3 --> DB1
    
    FS1 --> FS2
    FS1 --> FS3
    FS1 --> FS4
    FS2 --> EXT1
    
    DB1 --> DB2
    DB1 --> DB3
    DB1 --> DB4
    DB2 --> EXT2
    
    SI2 --> ST1
    SI2 --> ST2
    ST3 --> EXT4
    ST4 --> EXT3
    
    SI2 --> MS1
    MS1 --> MS2
    MS1 --> MS3
    MS1 --> MS4
```

### 2.2 数据流架构

```mermaid
flowchart TD
    A[处理后数据] --> B[StorageManager]
    B --> C{存储策略路由}
    
    C -->|热数据| D[本地文件存储]
    C -->|结构化数据| E[数据库存储]
    C -->|冷数据| F[归档存储]
    
    D --> G[文件分区管理]
    E --> H[数据库分表]
    F --> I[压缩归档]
    
    G --> J[本地文件系统]
    H --> K[PostgreSQL]
    I --> L[归档存储系统]
    
    J --> M[备份管理器]
    K --> M
    L --> M
    
    M --> N[备份存储]
    
    B --> O[存储监控]
    O --> P[空间监控]
    O --> Q[性能监控]
    O --> R[健康检查]
```

## 3. 核心接口设计

### 3.1 存储接口定义

```rust
// storage/src/lib.rs
use async_trait::async_trait;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use types::NormalizedData;
use chrono::{DateTime, Utc};

/// 数据存储接口
#[async_trait]
pub trait DataStorage: Send + Sync {
    /// 存储单条数据
    async fn store(&mut self, data: &NormalizedData) -> Result<StorageResult>;

    /// 批量存储数据
    async fn store_batch(&mut self, data: &[NormalizedData]) -> Result<Vec<StorageResult>>;

    /// 查询数据
    async fn query(&self, query: &StorageQuery) -> Result<Vec<NormalizedData>>;

    /// 删除数据
    async fn delete(&mut self, query: &StorageQuery) -> Result<u64>;

    /// 获取存储统计信息
    async fn get_statistics(&self) -> StorageStatistics;

    /// 获取存储状态
    async fn get_status(&self) -> StorageStatus;

    /// 获取存储类型
    fn get_storage_type(&self) -> StorageType;
}

/// 存储结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageResult {
    pub success: bool,
    pub storage_id: String,
    pub timestamp: u64,
    pub bytes_stored: usize,
    pub storage_location: String,
    pub error: Option<StorageError>,
}

/// 存储查询
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageQuery {
    pub query_type: QueryType,
    pub filters: Vec<QueryFilter>,
    pub sort_by: Option<String>,
    pub sort_order: SortOrder,
    pub limit: Option<usize>,
    pub offset: Option<usize>,
    pub time_range: Option<TimeRange>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QueryType {
    ById(String),
    ByGantryCode(String),
    ByPlateNumber(String),
    ByTimeRange(TimeRange),
    ByCustomFilter,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryFilter {
    pub field: String,
    pub operator: FilterOperator,
    pub value: FilterValue,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterOperator {
    Equal,
    NotEqual,
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    Like,
    In,
    NotIn,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FilterValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Array(Vec<String>),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SortOrder {
    Ascending,
    Descending,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TimeRange {
    pub start: DateTime<Utc>,
    pub end: DateTime<Utc>,
}

/// 存储状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StorageStatus {
    Available,
    Busy,
    Full,
    Error(String),
    Maintenance,
}

/// 存储类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StorageType {
    FileSystem { path: String },
    Database { connection_string: String },
    Archive { location: String },
    Hybrid,
}

/// 存储统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStatistics {
    pub total_records: u64,
    pub total_size_bytes: u64,
    pub available_space_bytes: u64,
    pub used_space_bytes: u64,
    pub average_record_size: f64,
    pub storage_efficiency: f64,
    pub last_backup_time: Option<DateTime<Utc>>,
    pub last_cleanup_time: Option<DateTime<Utc>>,
}

/// 存储错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StorageError {
    InsufficientSpace,
    PermissionDenied,
    ConnectionFailed(String),
    QueryFailed(String),
    DataCorruption(String),
    BackupFailed(String),
}
```

### 3.2 存储管理器接口

```rust
// storage/src/manager.rs
use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;

pub struct StorageManager {
    storages: HashMap<String, Arc<Mutex<dyn DataStorage>>>,
    router: StorageRouter,
    config: StorageConfig,
    metrics: Arc<StorageMetrics>,
    backup_manager: Arc<Mutex<BackupManager>>,
    retention_manager: Arc<Mutex<RetentionManager>>,
}

#[derive(Debug, Clone)]
pub struct StorageConfig {
    pub default_storage: String,
    pub file_storage: FileStorageConfig,
    pub database_storage: DatabaseStorageConfig,
    pub backup_config: BackupConfig,
    pub retention_config: RetentionConfig,
}

impl StorageManager {
    pub fn new(config: StorageConfig) -> Self {
        Self {
            storages: HashMap::new(),
            router: StorageRouter::new(config.clone()),
            config,
            metrics: Arc::new(StorageMetrics::new()),
            backup_manager: Arc::new(Mutex::new(BackupManager::new())),
            retention_manager: Arc::new(Mutex::new(RetentionManager::new())),
        }
    }

    /// 注册存储器
    pub async fn register_storage(&mut self, name: String, storage: Arc<Mutex<dyn DataStorage>>) -> Result<()> {
        self.storages.insert(name, storage);
        Ok(())
    }

    /// 存储数据（自动路由）
    pub async fn store(&self, data: &NormalizedData) -> Result<StorageResult> {
        let storage_name = self.router.route_storage(data).await?;
        
        if let Some(storage) = self.storages.get(&storage_name) {
            let mut storage_guard = storage.lock().await;
            let result = storage_guard.store(data).await?;
            
            // 更新指标
            self.metrics.record_storage(storage_name.clone(), result.bytes_stored, true).await;
            
            Ok(result)
        } else {
            Err(anyhow::anyhow!("Storage not found: {}", storage_name))
        }
    }

    /// 批量存储数据
    pub async fn store_batch(&self, data: &[NormalizedData]) -> Result<Vec<StorageResult>> {
        let mut results = Vec::new();
        
        // 按存储类型分组
        let mut storage_groups: HashMap<String, Vec<&NormalizedData>> = HashMap::new();
        
        for item in data {
            let storage_name = self.router.route_storage(item).await?;
            storage_groups.entry(storage_name).or_default().push(item);
        }

        // 并行存储到不同的存储器
        for (storage_name, group_data) in storage_groups {
            if let Some(storage) = self.storages.get(&storage_name) {
                let mut storage_guard = storage.lock().await;
                let batch_results = storage_guard.store_batch(&group_data).await?;
                results.extend(batch_results);
            }
        }

        Ok(results)
    }

    /// 查询数据
    pub async fn query(&self, query: &StorageQuery) -> Result<Vec<NormalizedData>> {
        let storage_names = self.router.route_query(query).await?;
        let mut all_results = Vec::new();

        for storage_name in storage_names {
            if let Some(storage) = self.storages.get(&storage_name) {
                let storage_guard = storage.lock().await;
                let results = storage_guard.query(query).await?;
                all_results.extend(results);
            }
        }

        // 合并和排序结果
        self.merge_and_sort_results(all_results, query).await
    }

    /// 启动后台任务
    pub async fn start_background_tasks(&self) -> Result<()> {
        // 启动备份任务
        self.start_backup_task().await?;
        
        // 启动数据清理任务
        self.start_cleanup_task().await?;
        
        // 启动监控任务
        self.start_monitoring_task().await?;

        Ok(())
    }

    async fn merge_and_sort_results(&self, mut results: Vec<NormalizedData>, query: &StorageQuery) -> Result<Vec<NormalizedData>> {
        // 根据查询条件排序
        if let Some(ref sort_field) = query.sort_by {
            results.sort_by(|a, b| {
                match sort_field.as_str() {
                    "timestamp" => {
                        match query.sort_order {
                            SortOrder::Ascending => a.timestamp.cmp(&b.timestamp),
                            SortOrder::Descending => b.timestamp.cmp(&a.timestamp),
                        }
                    }
                    "id" => {
                        match query.sort_order {
                            SortOrder::Ascending => a.id.cmp(&b.id),
                            SortOrder::Descending => b.id.cmp(&a.id),
                        }
                    }
                    _ => std::cmp::Ordering::Equal,
                }
            });
        }

        // 应用分页
        if let Some(offset) = query.offset {
            if offset < results.len() {
                results = results.into_iter().skip(offset).collect();
            } else {
                results.clear();
            }
        }

        if let Some(limit) = query.limit {
            results.truncate(limit);
        }

        Ok(results)
    }

    async fn start_backup_task(&self) -> Result<()> {
        let backup_manager = Arc::clone(&self.backup_manager);
        let storages = self.storages.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(3600)); // 每小时备份
            
            loop {
                interval.tick().await;
                
                let mut backup_guard = backup_manager.lock().await;
                for (name, storage) in &storages {
                    if let Err(e) = backup_guard.backup_storage(name, storage).await {
                        tracing::error!("Backup failed for storage {}: {}", name, e);
                    }
                }
            }
        });

        Ok(())
    }

    async fn start_cleanup_task(&self) -> Result<()> {
        let retention_manager = Arc::clone(&self.retention_manager);
        let storages = self.storages.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(86400)); // 每天清理
            
            loop {
                interval.tick().await;
                
                let mut retention_guard = retention_manager.lock().await;
                for (name, storage) in &storages {
                    if let Err(e) = retention_guard.cleanup_expired_data(name, storage).await {
                        tracing::error!("Cleanup failed for storage {}: {}", name, e);
                    }
                }
            }
        });

        Ok(())
    }

    async fn start_monitoring_task(&self) -> Result<()> {
        let metrics = Arc::clone(&self.metrics);
        let storages = self.storages.clone();
        
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(300)); // 每5分钟监控
            
            loop {
                interval.tick().await;
                
                for (name, storage) in &storages {
                    let storage_guard = storage.lock().await;
                    let stats = storage_guard.get_statistics().await;
                    metrics.update_storage_statistics(name.clone(), stats).await;
                }
            }
        });

        Ok(())
    }
}
```

## 4. 本地文件存储实现

### 4.1 文件存储器设计

```rust
// storage/src/file_storage.rs
use async_trait::async_trait;
use anyhow::{Result, anyhow};
use std::path::{Path, PathBuf};
use tokio::fs;
use tokio::io::{AsyncWriteExt, AsyncReadExt};
use serde_json;
use chrono::{DateTime, Utc, Datelike};

pub struct FileStorage {
    config: FileStorageConfig,
    file_manager: FileManager,
    indexer: FileIndexer,
    compression_engine: CompressionEngine,
    statistics: StorageStatistics,
}

#[derive(Debug, Clone)]
pub struct FileStorageConfig {
    pub base_path: PathBuf,
    pub partition_strategy: PartitionStrategy,
    pub compression_enabled: bool,
    pub max_file_size: usize,
    pub index_enabled: bool,
    pub backup_enabled: bool,
}

#[derive(Debug, Clone)]
pub enum PartitionStrategy {
    Daily,
    Hourly,
    BySize(usize),
    ByGantryCode,
    Hybrid,
}

impl FileStorage {
    pub fn new(config: FileStorageConfig) -> Result<Self> {
        let file_manager = FileManager::new(config.clone())?;
        let indexer = FileIndexer::new(config.base_path.join("index"))?;
        let compression_engine = CompressionEngine::new(config.compression_enabled);

        Ok(Self {
            config,
            file_manager,
            indexer,
            compression_engine,
            statistics: StorageStatistics::default(),
        })
    }
}

#[async_trait]
impl DataStorage for FileStorage {
    async fn store(&mut self, data: &NormalizedData) -> Result<StorageResult> {
        let start_time = std::time::Instant::now();

        // 确定存储路径
        let storage_path = self.determine_storage_path(data).await?;

        // 序列化数据
        let serialized_data = serde_json::to_vec(data)?;

        // 压缩数据（如果启用）
        let final_data = if self.config.compression_enabled {
            self.compression_engine.compress(&serialized_data).await?
        } else {
            serialized_data
        };

        // 写入文件
        let storage_id = uuid::Uuid::new_v4().to_string();
        let file_path = storage_path.join(format!("{}.json", storage_id));

        fs::create_dir_all(&storage_path).await?;
        let mut file = fs::File::create(&file_path).await?;
        file.write_all(&final_data).await?;
        file.flush().await?;

        // 更新索引
        if self.config.index_enabled {
            self.indexer.add_entry(storage_id.clone(), file_path.clone(), data).await?;
        }

        // 更新统计信息
        self.statistics.total_records += 1;
        self.statistics.total_size_bytes += final_data.len() as u64;

        let processing_time = start_time.elapsed().as_millis() as u64;

        Ok(StorageResult {
            success: true,
            storage_id,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            bytes_stored: final_data.len(),
            storage_location: file_path.to_string_lossy().to_string(),
            error: None,
        })
    }

    async fn store_batch(&mut self, data: &[NormalizedData]) -> Result<Vec<StorageResult>> {
        let mut results = Vec::new();

        // 按分区策略分组
        let mut partition_groups: std::collections::HashMap<PathBuf, Vec<&NormalizedData>> = std::collections::HashMap::new();

        for item in data {
            let partition_path = self.determine_storage_path(item).await?;
            partition_groups.entry(partition_path).or_default().push(item);
        }

        // 并行写入不同分区
        for (partition_path, group_data) in partition_groups {
            let batch_file_path = partition_path.join(format!("batch_{}.jsonl", chrono::Utc::now().timestamp_millis()));

            fs::create_dir_all(&partition_path).await?;
            let mut batch_file = fs::File::create(&batch_file_path).await?;

            for item in group_data {
                let storage_id = uuid::Uuid::new_v4().to_string();
                let serialized_data = serde_json::to_vec(item)?;

                let final_data = if self.config.compression_enabled {
                    self.compression_engine.compress(&serialized_data).await?
                } else {
                    serialized_data
                };

                // 写入批量文件（JSONL格式）
                batch_file.write_all(&final_data).await?;
                batch_file.write_all(b"\n").await?;

                // 更新索引
                if self.config.index_enabled {
                    self.indexer.add_entry(storage_id.clone(), batch_file_path.clone(), item).await?;
                }

                results.push(StorageResult {
                    success: true,
                    storage_id,
                    timestamp: chrono::Utc::now().timestamp_millis() as u64,
                    bytes_stored: final_data.len(),
                    storage_location: batch_file_path.to_string_lossy().to_string(),
                    error: None,
                });

                self.statistics.total_records += 1;
                self.statistics.total_size_bytes += final_data.len() as u64;
            }

            batch_file.flush().await?;
        }

        Ok(results)
    }

    async fn query(&self, query: &StorageQuery) -> Result<Vec<NormalizedData>> {
        if self.config.index_enabled {
            // 使用索引查询
            self.indexer.query(query).await
        } else {
            // 全文件扫描查询
            self.scan_files_for_query(query).await
        }
    }

    async fn delete(&mut self, query: &StorageQuery) -> Result<u64> {
        let matching_data = self.query(query).await?;
        let mut deleted_count = 0;

        for data in matching_data {
            if let Some(file_path) = self.indexer.get_file_path(&data.id).await? {
                if fs::remove_file(&file_path).await.is_ok() {
                    self.indexer.remove_entry(&data.id).await?;
                    deleted_count += 1;

                    // 更新统计信息
                    self.statistics.total_records -= 1;
                }
            }
        }

        Ok(deleted_count)
    }

    async fn get_statistics(&self) -> StorageStatistics {
        let mut stats = self.statistics.clone();

        // 计算可用空间
        if let Ok(metadata) = fs::metadata(&self.config.base_path).await {
            // 这里应该使用系统调用获取实际的磁盘空间信息
            // 简化实现，假设总空间为1TB
            let total_space = 1024 * 1024 * 1024 * 1024u64; // 1TB
            stats.available_space_bytes = total_space - stats.used_space_bytes;
        }

        stats.average_record_size = if stats.total_records > 0 {
            stats.total_size_bytes as f64 / stats.total_records as f64
        } else {
            0.0
        };

        stats.storage_efficiency = if self.config.compression_enabled {
            0.7 // 假设压缩率为30%
        } else {
            1.0
        };

        stats
    }

    async fn get_status(&self) -> StorageStatus {
        // 检查存储空间
        let stats = self.get_statistics().await;
        let usage_ratio = stats.used_space_bytes as f64 / (stats.used_space_bytes + stats.available_space_bytes) as f64;

        if usage_ratio > 0.95 {
            StorageStatus::Full
        } else if usage_ratio > 0.8 {
            StorageStatus::Busy
        } else {
            StorageStatus::Available
        }
    }

    fn get_storage_type(&self) -> StorageType {
        StorageType::FileSystem {
            path: self.config.base_path.to_string_lossy().to_string(),
        }
    }
}

impl FileStorage {
    /// 确定数据的存储路径
    async fn determine_storage_path(&self, data: &NormalizedData) -> Result<PathBuf> {
        let base_path = &self.config.base_path;

        match &self.config.partition_strategy {
            PartitionStrategy::Daily => {
                let date = DateTime::from_timestamp_millis(data.timestamp as i64)
                    .unwrap_or_else(|| Utc::now());
                Ok(base_path.join(format!("daily/{}/{:02}/{:02}",
                    date.year(), date.month(), date.day())))
            }
            PartitionStrategy::Hourly => {
                let date = DateTime::from_timestamp_millis(data.timestamp as i64)
                    .unwrap_or_else(|| Utc::now());
                Ok(base_path.join(format!("hourly/{}/{:02}/{:02}/{:02}",
                    date.year(), date.month(), date.day(), date.hour())))
            }
            PartitionStrategy::ByGantryCode => {
                Ok(base_path.join(format!("gantry/{}", data.gantry_code)))
            }
            PartitionStrategy::BySize(_) => {
                // 基于文件大小的分区策略
                let partition_id = self.file_manager.get_current_partition().await?;
                Ok(base_path.join(format!("size_partition/{}", partition_id)))
            }
            PartitionStrategy::Hybrid => {
                // 混合策略：按门架代码和日期
                let date = DateTime::from_timestamp_millis(data.timestamp as i64)
                    .unwrap_or_else(|| Utc::now());
                Ok(base_path.join(format!("hybrid/{}/{}/{:02}/{:02}",
                    data.gantry_code, date.year(), date.month(), date.day())))
            }
        }
    }

    /// 扫描文件进行查询
    async fn scan_files_for_query(&self, query: &StorageQuery) -> Result<Vec<NormalizedData>> {
        let mut results = Vec::new();

        // 根据查询类型确定扫描路径
        let scan_paths = self.determine_scan_paths(query).await?;

        for scan_path in scan_paths {
            if scan_path.exists() {
                let mut entries = fs::read_dir(&scan_path).await?;

                while let Some(entry) = entries.next_entry().await? {
                    let path = entry.path();
                    if path.is_file() && path.extension().map_or(false, |ext| ext == "json" || ext == "jsonl") {
                        let file_results = self.scan_single_file(&path, query).await?;
                        results.extend(file_results);
                    }
                }
            }
        }

        Ok(results)
    }

    /// 扫描单个文件
    async fn scan_single_file(&self, file_path: &Path, query: &StorageQuery) -> Result<Vec<NormalizedData>> {
        let mut results = Vec::new();
        let mut file = fs::File::open(file_path).await?;
        let mut contents = Vec::new();
        file.read_to_end(&mut contents).await?;

        // 解压缩（如果需要）
        let final_contents = if self.config.compression_enabled {
            self.compression_engine.decompress(&contents).await?
        } else {
            contents
        };

        // 解析文件内容
        if file_path.extension().map_or(false, |ext| ext == "jsonl") {
            // JSONL格式（每行一个JSON对象）
            let content_str = String::from_utf8(final_contents)?;
            for line in content_str.lines() {
                if let Ok(data) = serde_json::from_str::<NormalizedData>(line) {
                    if self.matches_query(&data, query) {
                        results.push(data);
                    }
                }
            }
        } else {
            // 单个JSON对象
            if let Ok(data) = serde_json::from_slice::<NormalizedData>(&final_contents) {
                if self.matches_query(&data, query) {
                    results.push(data);
                }
            }
        }

        Ok(results)
    }

    /// 检查数据是否匹配查询条件
    fn matches_query(&self, data: &NormalizedData, query: &StorageQuery) -> bool {
        // 检查时间范围
        if let Some(ref time_range) = query.time_range {
            let data_time = DateTime::from_timestamp_millis(data.timestamp as i64).unwrap_or_else(|| Utc::now());
            if data_time < time_range.start || data_time > time_range.end {
                return false;
            }
        }

        // 检查查询类型
        match &query.query_type {
            QueryType::ById(id) => data.id == *id,
            QueryType::ByGantryCode(code) => data.gantry_code == *code,
            QueryType::ByPlateNumber(plate) => {
                data.plate_no.as_ref().map_or(false, |p| p == plate)
            }
            QueryType::ByTimeRange(range) => {
                let data_time = DateTime::from_timestamp_millis(data.timestamp as i64).unwrap_or_else(|| Utc::now());
                data_time >= range.start && data_time <= range.end
            }
            QueryType::ByCustomFilter => {
                // 检查自定义过滤器
                self.matches_filters(data, &query.filters)
            }
        }
    }

    /// 检查数据是否匹配过滤器
    fn matches_filters(&self, data: &NormalizedData, filters: &[QueryFilter]) -> bool {
        for filter in filters {
            if !self.matches_single_filter(data, filter) {
                return false;
            }
        }
        true
    }

    /// 检查数据是否匹配单个过滤器
    fn matches_single_filter(&self, data: &NormalizedData, filter: &QueryFilter) -> bool {
        let field_value = self.get_field_value(data, &filter.field);

        match (&filter.operator, &filter.value, field_value) {
            (FilterOperator::Equal, FilterValue::String(val), Some(field_val)) => field_val == val,
            (FilterOperator::NotEqual, FilterValue::String(val), Some(field_val)) => field_val != val,
            (FilterOperator::Like, FilterValue::String(val), Some(field_val)) => field_val.contains(val),
            // 添加更多操作符的实现...
            _ => false,
        }
    }

    /// 获取字段值
    fn get_field_value(&self, data: &NormalizedData, field: &str) -> Option<String> {
        match field {
            "id" => Some(data.id.clone()),
            "gantry_code" => Some(data.gantry_code.clone()),
            "plate_no" => data.plate_no.clone(),
            "vehicle_type" => data.vehicle_type.clone(),
            "camera_code" => data.camera_code.clone(),
            "macid" => data.macid.clone(),
            _ => None,
        }
    }

    /// 确定扫描路径
    async fn determine_scan_paths(&self, query: &StorageQuery) -> Result<Vec<PathBuf>> {
        let mut paths = Vec::new();
        let base_path = &self.config.base_path;

        match &query.query_type {
            QueryType::ByGantryCode(code) => {
                if matches!(self.config.partition_strategy, PartitionStrategy::ByGantryCode | PartitionStrategy::Hybrid) {
                    paths.push(base_path.join(format!("gantry/{}", code)));
                } else {
                    // 需要扫描所有分区
                    paths.push(base_path.clone());
                }
            }
            QueryType::ByTimeRange(range) => {
                if matches!(self.config.partition_strategy, PartitionStrategy::Daily | PartitionStrategy::Hourly) {
                    // 根据时间范围确定需要扫描的分区
                    let mut current = range.start.date_naive();
                    let end_date = range.end.date_naive();

                    while current <= end_date {
                        paths.push(base_path.join(format!("daily/{}/{:02}/{:02}",
                            current.year(), current.month(), current.day())));
                        current = current.succ_opt().unwrap_or(end_date);
                    }
                } else {
                    paths.push(base_path.clone());
                }
            }
            _ => {
                // 默认扫描所有路径
                paths.push(base_path.clone());
            }
        }

        Ok(paths)
    }
}
```

## 5. 数据库存储实现

### 5.1 数据库存储器设计

```rust
// storage/src/database_storage.rs
use async_trait::async_trait;
use anyhow::{Result, anyhow};
use sqlx::{PgPool, Row, Postgres, QueryBuilder};
use serde_json;

pub struct DatabaseStorage {
    config: DatabaseStorageConfig,
    connection_pool: PgPool,
    schema_manager: SchemaManager,
    query_builder: QueryBuilder<Postgres>,
    statistics: StorageStatistics,
}

#[derive(Debug, Clone)]
pub struct DatabaseStorageConfig {
    pub connection_string: String,
    pub max_connections: u32,
    pub table_prefix: String,
    pub partition_enabled: bool,
    pub index_strategy: IndexStrategy,
    pub backup_enabled: bool,
}

#[derive(Debug, Clone)]
pub enum IndexStrategy {
    TimeBasedIndex,
    GantryCodeIndex,
    PlateNumberIndex,
    CompositeIndex,
    FullTextSearch,
}

impl DatabaseStorage {
    pub async fn new(config: DatabaseStorageConfig) -> Result<Self> {
        let connection_pool = PgPool::connect(&config.connection_string).await?;
        let schema_manager = SchemaManager::new(connection_pool.clone(), config.clone()).await?;

        // 初始化数据库表结构
        schema_manager.initialize_schema().await?;

        Ok(Self {
            config,
            connection_pool,
            schema_manager,
            query_builder: QueryBuilder::new(""),
            statistics: StorageStatistics::default(),
        })
    }
}

#[async_trait]
impl DataStorage for DatabaseStorage {
    async fn store(&mut self, data: &NormalizedData) -> Result<StorageResult> {
        let start_time = std::time::Instant::now();

        // 确定目标表
        let table_name = self.determine_table_name(data).await?;

        // 构建插入查询
        let query = format!(
            r#"
            INSERT INTO {} (
                id, gantry_code, timestamp, plate_no, vehicle_type,
                camera_code, plate_type, plate_color, vehicle_cat,
                vehicle_size, vehicle_object, macid, vehicle_fee_type,
                recievable_fee, actual_fee, pro_recievable_fee, pro_actual_fee,
                image_bytes, created_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
                $11, $12, $13, $14, $15, $16, $17, $18, $19
            )
            "#,
            table_name
        );

        let result = sqlx::query(&query)
            .bind(&data.id)
            .bind(&data.gantry_code)
            .bind(data.timestamp as i64)
            .bind(&data.plate_no)
            .bind(&data.vehicle_type)
            .bind(&data.camera_code)
            .bind(&data.plate_type)
            .bind(&data.plate_color)
            .bind(&data.vehicle_cat)
            .bind(&data.vehicle_size)
            .bind(&data.vehicle_object)
            .bind(&data.macid)
            .bind(&data.vehicle_fee_type)
            .bind(data.recievable_fee.map(|f| f as i32))
            .bind(data.actual_fee.map(|f| f as i32))
            .bind(data.pro_recievable_fee.map(|f| f as i32))
            .bind(data.pro_actual_fee.map(|f| f as i32))
            .bind(&data.image_bytes)
            .bind(chrono::Utc::now())
            .execute(&self.connection_pool)
            .await?;

        let bytes_stored = self.estimate_record_size(data);
        let processing_time = start_time.elapsed().as_millis() as u64;

        // 更新统计信息
        self.statistics.total_records += 1;
        self.statistics.total_size_bytes += bytes_stored as u64;

        Ok(StorageResult {
            success: true,
            storage_id: data.id.clone(),
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            bytes_stored,
            storage_location: format!("{}:{}", table_name, data.id),
            error: None,
        })
    }

    async fn store_batch(&mut self, data: &[NormalizedData]) -> Result<Vec<StorageResult>> {
        let mut results = Vec::new();

        // 按表分组
        let mut table_groups: std::collections::HashMap<String, Vec<&NormalizedData>> = std::collections::HashMap::new();

        for item in data {
            let table_name = self.determine_table_name(item).await?;
            table_groups.entry(table_name).or_default().push(item);
        }

        // 批量插入到各表
        for (table_name, group_data) in table_groups {
            let batch_results = self.batch_insert_to_table(&table_name, &group_data).await?;
            results.extend(batch_results);
        }

        Ok(results)
    }

    async fn query(&self, query: &StorageQuery) -> Result<Vec<NormalizedData>> {
        let sql_query = self.build_sql_query(query).await?;
        let rows = sqlx::query(&sql_query)
            .fetch_all(&self.connection_pool)
            .await?;

        let mut results = Vec::new();
        for row in rows {
            let data = self.row_to_normalized_data(row)?;
            results.push(data);
        }

        Ok(results)
    }

    async fn delete(&mut self, query: &StorageQuery) -> Result<u64> {
        let delete_query = self.build_delete_query(query).await?;
        let result = sqlx::query(&delete_query)
            .execute(&self.connection_pool)
            .await?;

        let deleted_count = result.rows_affected();
        self.statistics.total_records -= deleted_count;

        Ok(deleted_count)
    }

    async fn get_statistics(&self) -> StorageStatistics {
        let mut stats = self.statistics.clone();

        // 查询数据库统计信息
        if let Ok(row) = sqlx::query("SELECT COUNT(*) as total_records, pg_database_size(current_database()) as db_size")
            .fetch_one(&self.connection_pool)
            .await
        {
            stats.total_records = row.get::<i64, _>("total_records") as u64;
            stats.total_size_bytes = row.get::<i64, _>("db_size") as u64;
        }

        stats.average_record_size = if stats.total_records > 0 {
            stats.total_size_bytes as f64 / stats.total_records as f64
        } else {
            0.0
        };

        stats
    }

    async fn get_status(&self) -> StorageStatus {
        // 检查数据库连接状态
        match sqlx::query("SELECT 1").fetch_one(&self.connection_pool).await {
            Ok(_) => StorageStatus::Available,
            Err(_) => StorageStatus::Error("Database connection failed".to_string()),
        }
    }

    fn get_storage_type(&self) -> StorageType {
        StorageType::Database {
            connection_string: self.mask_connection_string(),
        }
    }
}

impl DatabaseStorage {
    /// 确定目标表名
    async fn determine_table_name(&self, data: &NormalizedData) -> Result<String> {
        if self.config.partition_enabled {
            let date = chrono::DateTime::from_timestamp_millis(data.timestamp as i64)
                .unwrap_or_else(|| chrono::Utc::now());
            Ok(format!("{}_{}_{}_{:02}",
                self.config.table_prefix,
                date.year(),
                date.month(),
                date.day()
            ))
        } else {
            Ok(format!("{}_data", self.config.table_prefix))
        }
    }

    /// 批量插入到表
    async fn batch_insert_to_table(&mut self, table_name: &str, data: &[&NormalizedData]) -> Result<Vec<StorageResult>> {
        let mut results = Vec::new();

        // 构建批量插入查询
        let mut query_builder = QueryBuilder::new(
            format!("INSERT INTO {} (id, gantry_code, timestamp, plate_no, vehicle_type, camera_code, plate_type, plate_color, vehicle_cat, vehicle_size, vehicle_object, macid, vehicle_fee_type, recievable_fee, actual_fee, pro_recievable_fee, pro_actual_fee, image_bytes, created_at)", table_name)
        );

        query_builder.push_values(data.iter(), |mut b, item| {
            b.push_bind(&item.id)
                .push_bind(&item.gantry_code)
                .push_bind(item.timestamp as i64)
                .push_bind(&item.plate_no)
                .push_bind(&item.vehicle_type)
                .push_bind(&item.camera_code)
                .push_bind(&item.plate_type)
                .push_bind(&item.plate_color)
                .push_bind(&item.vehicle_cat)
                .push_bind(&item.vehicle_size)
                .push_bind(&item.vehicle_object)
                .push_bind(&item.macid)
                .push_bind(&item.vehicle_fee_type)
                .push_bind(item.recievable_fee.map(|f| f as i32))
                .push_bind(item.actual_fee.map(|f| f as i32))
                .push_bind(item.pro_recievable_fee.map(|f| f as i32))
                .push_bind(item.pro_actual_fee.map(|f| f as i32))
                .push_bind(&item.image_bytes)
                .push_bind(chrono::Utc::now());
        });

        let query = query_builder.build();
        query.execute(&self.connection_pool).await?;

        // 生成结果
        for item in data {
            let bytes_stored = self.estimate_record_size(item);
            results.push(StorageResult {
                success: true,
                storage_id: item.id.clone(),
                timestamp: chrono::Utc::now().timestamp_millis() as u64,
                bytes_stored,
                storage_location: format!("{}:{}", table_name, item.id),
                error: None,
            });

            self.statistics.total_records += 1;
            self.statistics.total_size_bytes += bytes_stored as u64;
        }

        Ok(results)
    }

    /// 构建SQL查询
    async fn build_sql_query(&self, query: &StorageQuery) -> Result<String> {
        let mut sql = String::new();
        let table_pattern = if self.config.partition_enabled {
            format!("{}_*", self.config.table_prefix)
        } else {
            format!("{}_data", self.config.table_prefix)
        };

        sql.push_str(&format!("SELECT * FROM {}", table_pattern));

        // 构建WHERE子句
        let mut where_conditions = Vec::new();

        match &query.query_type {
            QueryType::ById(id) => {
                where_conditions.push(format!("id = '{}'", id));
            }
            QueryType::ByGantryCode(code) => {
                where_conditions.push(format!("gantry_code = '{}'", code));
            }
            QueryType::ByPlateNumber(plate) => {
                where_conditions.push(format!("plate_no = '{}'", plate));
            }
            QueryType::ByTimeRange(range) => {
                where_conditions.push(format!(
                    "timestamp BETWEEN {} AND {}",
                    range.start.timestamp_millis(),
                    range.end.timestamp_millis()
                ));
            }
            QueryType::ByCustomFilter => {
                for filter in &query.filters {
                    let condition = self.build_filter_condition(filter)?;
                    where_conditions.push(condition);
                }
            }
        }

        if let Some(ref time_range) = query.time_range {
            where_conditions.push(format!(
                "timestamp BETWEEN {} AND {}",
                time_range.start.timestamp_millis(),
                time_range.end.timestamp_millis()
            ));
        }

        if !where_conditions.is_empty() {
            sql.push_str(" WHERE ");
            sql.push_str(&where_conditions.join(" AND "));
        }

        // 添加排序
        if let Some(ref sort_field) = query.sort_by {
            sql.push_str(&format!(" ORDER BY {} {}",
                sort_field,
                match query.sort_order {
                    SortOrder::Ascending => "ASC",
                    SortOrder::Descending => "DESC",
                }
            ));
        }

        // 添加分页
        if let Some(limit) = query.limit {
            sql.push_str(&format!(" LIMIT {}", limit));
        }

        if let Some(offset) = query.offset {
            sql.push_str(&format!(" OFFSET {}", offset));
        }

        Ok(sql)
    }

    /// 构建过滤条件
    fn build_filter_condition(&self, filter: &QueryFilter) -> Result<String> {
        let condition = match (&filter.operator, &filter.value) {
            (FilterOperator::Equal, FilterValue::String(val)) => {
                format!("{} = '{}'", filter.field, val)
            }
            (FilterOperator::NotEqual, FilterValue::String(val)) => {
                format!("{} != '{}'", filter.field, val)
            }
            (FilterOperator::Like, FilterValue::String(val)) => {
                format!("{} LIKE '%{}%'", filter.field, val)
            }
            (FilterOperator::GreaterThan, FilterValue::Number(val)) => {
                format!("{} > {}", filter.field, val)
            }
            (FilterOperator::LessThan, FilterValue::Number(val)) => {
                format!("{} < {}", filter.field, val)
            }
            (FilterOperator::In, FilterValue::Array(vals)) => {
                let values = vals.iter().map(|v| format!("'{}'", v)).collect::<Vec<_>>().join(",");
                format!("{} IN ({})", filter.field, values)
            }
            _ => return Err(anyhow!("Unsupported filter combination")),
        };

        Ok(condition)
    }

    /// 构建删除查询
    async fn build_delete_query(&self, query: &StorageQuery) -> Result<String> {
        let mut sql = String::new();
        let table_pattern = if self.config.partition_enabled {
            format!("{}_*", self.config.table_prefix)
        } else {
            format!("{}_data", self.config.table_prefix)
        };

        sql.push_str(&format!("DELETE FROM {}", table_pattern));

        // 构建WHERE子句（与查询类似）
        let mut where_conditions = Vec::new();

        match &query.query_type {
            QueryType::ById(id) => {
                where_conditions.push(format!("id = '{}'", id));
            }
            QueryType::ByGantryCode(code) => {
                where_conditions.push(format!("gantry_code = '{}'", code));
            }
            QueryType::ByTimeRange(range) => {
                where_conditions.push(format!(
                    "timestamp BETWEEN {} AND {}",
                    range.start.timestamp_millis(),
                    range.end.timestamp_millis()
                ));
            }
            _ => return Err(anyhow!("Unsupported delete query type")),
        }

        if !where_conditions.is_empty() {
            sql.push_str(" WHERE ");
            sql.push_str(&where_conditions.join(" AND "));
        }

        Ok(sql)
    }

    /// 将数据库行转换为NormalizedData
    fn row_to_normalized_data(&self, row: sqlx::postgres::PgRow) -> Result<NormalizedData> {
        Ok(NormalizedData {
            id: row.get("id"),
            gantry_code: row.get("gantry_code"),
            timestamp: row.get::<i64, _>("timestamp") as u64,
            plate_no: row.get("plate_no"),
            vehicle_type: row.get("vehicle_type"),
            camera_code: row.get("camera_code"),
            plate_type: row.get("plate_type"),
            plate_color: row.get("plate_color"),
            vehicle_cat: row.get("vehicle_cat"),
            vehicle_size: row.get("vehicle_size"),
            vehicle_object: row.get("vehicle_object"),
            macid: row.get("macid"),
            vehicle_fee_type: row.get("vehicle_fee_type"),
            recievable_fee: row.get::<Option<i32>, _>("recievable_fee").map(|f| f as u32),
            actual_fee: row.get::<Option<i32>, _>("actual_fee").map(|f| f as u32),
            pro_recievable_fee: row.get::<Option<i32>, _>("pro_recievable_fee").map(|f| f as u32),
            pro_actual_fee: row.get::<Option<i32>, _>("pro_actual_fee").map(|f| f as u32),
            image_bytes: row.get("image_bytes"),
        })
    }

    /// 估算记录大小
    fn estimate_record_size(&self, data: &NormalizedData) -> usize {
        let mut size = 0;
        size += data.id.len();
        size += data.gantry_code.len();
        size += 8; // timestamp
        size += data.plate_no.as_ref().map_or(0, |s| s.len());
        size += data.vehicle_type.as_ref().map_or(0, |s| s.len());
        size += data.camera_code.as_ref().map_or(0, |s| s.len());
        size += data.image_bytes.as_ref().map_or(0, |b| b.len());
        size += 64; // 其他字段的估算大小
        size
    }

    /// 掩码连接字符串（隐藏密码）
    fn mask_connection_string(&self) -> String {
        let mut masked = self.config.connection_string.clone();
        if let Some(password_start) = masked.find("password=") {
            if let Some(password_end) = masked[password_start..].find(' ').or_else(|| masked[password_start..].find('&')) {
                let end_pos = password_start + password_end;
                masked.replace_range(password_start + 9..end_pos, "****");
            } else {
                masked.replace_range(password_start + 9.., "****");
            }
        }
        masked
    }
}
```

## 6. 配置管理

### 6.1 存储配置

```yaml
# config/storage.yaml
storage:
  # 全局配置
  global:
    default_storage: "hybrid"
    backup_enabled: true
    monitoring_enabled: true
    cleanup_enabled: true

  # 文件存储配置
  file_storage:
    enabled: true
    base_path: "/data/hcs/storage"
    partition_strategy: "hybrid"  # daily | hourly | by_size | by_gantry_code | hybrid
    compression_enabled: true
    max_file_size: 104857600  # 100MB
    index_enabled: true
    backup_enabled: true

    # 分区配置
    partition_config:
      daily:
        retention_days: 365
        compression_after_days: 7
      hourly:
        retention_hours: 8760  # 1年
        compression_after_hours: 168  # 1周
      by_size:
        max_partition_size: 1073741824  # 1GB
        max_partitions: 1000
      hybrid:
        primary_strategy: "by_gantry_code"
        secondary_strategy: "daily"

  # 数据库存储配置
  database_storage:
    enabled: true
    connection_string: "postgresql://hcs_user:${DB_PASSWORD}@localhost:5432/hcs_db"
    max_connections: 20
    table_prefix: "hcs_data"
    partition_enabled: true
    index_strategy: "composite"  # time_based | gantry_code | plate_number | composite | full_text
    backup_enabled: true

    # 分区配置
    partition_config:
      strategy: "monthly"  # daily | weekly | monthly
      retention_months: 12
      archive_after_months: 3

  # 存储路由配置
  routing:
    strategy: "data_type_based"  # size_based | data_type_based | time_based | custom
    rules:
      - condition: "data.is_transaction() && data.image_bytes.is_none()"
        storage: "database"
        priority: 1
      - condition: "data.is_vehicle_data() && data.image_bytes.is_some()"
        storage: "file_system"
        priority: 2
      - condition: "data.timestamp < now() - 30*24*3600*1000"  # 30天前的数据
        storage: "archive"
        priority: 3
      - condition: "default"
        storage: "hybrid"
        priority: 999

  # 备份配置
  backup:
    enabled: true
    strategy: "incremental"  # full | incremental | differential
    schedule: "0 2 * * *"  # 每天凌晨2点
    retention_days: 30
    compression_enabled: true
    encryption_enabled: true

    destinations:
      - type: "local"
        path: "/backup/hcs/storage"
        enabled: true
      - type: "remote"
        url: "s3://hcs-backup-bucket/storage"
        enabled: false
        credentials:
          access_key: "${AWS_ACCESS_KEY}"
          secret_key: "${AWS_SECRET_KEY}"

  # 数据保留策略
  retention:
    enabled: true
    policies:
      - name: "transaction_data"
        condition: "data.is_transaction()"
        retention_days: 2555  # 7年
        archive_after_days: 365  # 1年后归档
        cleanup_after_days: 2555
      - name: "vehicle_data_with_image"
        condition: "data.is_vehicle_data() && data.image_bytes.is_some()"
        retention_days: 1095  # 3年
        archive_after_days: 90  # 3个月后归档
        cleanup_after_days: 1095
      - name: "vehicle_data_without_image"
        condition: "data.is_vehicle_data() && data.image_bytes.is_none()"
        retention_days: 1825  # 5年
        archive_after_days: 180  # 6个月后归档
        cleanup_after_days: 1825

  # 监控配置
  monitoring:
    enabled: true
    metrics_collection_interval: 300  # 5分钟
    health_check_interval: 60  # 1分钟
    alert_thresholds:
      storage_usage_percent: 85
      error_rate_percent: 5
      response_time_ms: 5000
      backup_failure_count: 3

    # 存储空间监控
    space_monitoring:
      enabled: true
      check_interval: 300
      warning_threshold_percent: 80
      critical_threshold_percent: 90
      cleanup_threshold_percent: 95

  # 性能优化配置
  performance:
    batch_size: 1000
    max_concurrent_operations: 50
    connection_pool_size: 20
    query_timeout_seconds: 30
    transaction_timeout_seconds: 60

    # 缓存配置
    cache:
      enabled: true
      max_size: 1000
      ttl_seconds: 3600
      eviction_policy: "lru"  # lru | lfu | fifo
```

### 6.2 配置加载器

```rust
// storage/src/config.rs
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct StorageConfig {
    pub global: GlobalStorageConfig,
    pub file_storage: FileStorageConfig,
    pub database_storage: DatabaseStorageConfig,
    pub routing: RoutingConfig,
    pub backup: BackupConfig,
    pub retention: RetentionConfig,
    pub monitoring: MonitoringConfig,
    pub performance: PerformanceConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct GlobalStorageConfig {
    pub default_storage: String,
    pub backup_enabled: bool,
    pub monitoring_enabled: bool,
    pub cleanup_enabled: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct FileStorageConfig {
    pub enabled: bool,
    pub base_path: String,
    pub partition_strategy: String,
    pub compression_enabled: bool,
    pub max_file_size: usize,
    pub index_enabled: bool,
    pub backup_enabled: bool,
    pub partition_config: PartitionConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DatabaseStorageConfig {
    pub enabled: bool,
    pub connection_string: String,
    pub max_connections: u32,
    pub table_prefix: String,
    pub partition_enabled: bool,
    pub index_strategy: String,
    pub backup_enabled: bool,
    pub partition_config: DatabasePartitionConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PartitionConfig {
    pub daily: DailyPartitionConfig,
    pub hourly: HourlyPartitionConfig,
    pub by_size: SizePartitionConfig,
    pub hybrid: HybridPartitionConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DailyPartitionConfig {
    pub retention_days: u32,
    pub compression_after_days: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct HourlyPartitionConfig {
    pub retention_hours: u32,
    pub compression_after_hours: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SizePartitionConfig {
    pub max_partition_size: usize,
    pub max_partitions: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct HybridPartitionConfig {
    pub primary_strategy: String,
    pub secondary_strategy: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DatabasePartitionConfig {
    pub strategy: String,
    pub retention_months: u32,
    pub archive_after_months: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RoutingConfig {
    pub strategy: String,
    pub rules: Vec<RoutingRule>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RoutingRule {
    pub condition: String,
    pub storage: String,
    pub priority: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct BackupConfig {
    pub enabled: bool,
    pub strategy: String,
    pub schedule: String,
    pub retention_days: u32,
    pub compression_enabled: bool,
    pub encryption_enabled: bool,
    pub destinations: Vec<BackupDestination>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct BackupDestination {
    pub r#type: String,
    pub path: Option<String>,
    pub url: Option<String>,
    pub enabled: bool,
    pub credentials: Option<HashMap<String, String>>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RetentionConfig {
    pub enabled: bool,
    pub policies: Vec<RetentionPolicy>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct RetentionPolicy {
    pub name: String,
    pub condition: String,
    pub retention_days: u32,
    pub archive_after_days: u32,
    pub cleanup_after_days: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct MonitoringConfig {
    pub enabled: bool,
    pub metrics_collection_interval: u64,
    pub health_check_interval: u64,
    pub alert_thresholds: AlertThresholds,
    pub space_monitoring: SpaceMonitoringConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AlertThresholds {
    pub storage_usage_percent: f64,
    pub error_rate_percent: f64,
    pub response_time_ms: u64,
    pub backup_failure_count: u32,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SpaceMonitoringConfig {
    pub enabled: bool,
    pub check_interval: u64,
    pub warning_threshold_percent: f64,
    pub critical_threshold_percent: f64,
    pub cleanup_threshold_percent: f64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct PerformanceConfig {
    pub batch_size: usize,
    pub max_concurrent_operations: usize,
    pub connection_pool_size: u32,
    pub query_timeout_seconds: u64,
    pub transaction_timeout_seconds: u64,
    pub cache: CacheConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct CacheConfig {
    pub enabled: bool,
    pub max_size: usize,
    pub ttl_seconds: u64,
    pub eviction_policy: String,
}

impl StorageConfig {
    /// 从文件加载配置
    pub fn load_from_file(path: &str) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: StorageConfig = serde_yaml::from_str(&content)?;
        config.validate()?;
        Ok(config)
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证存储配置
        if !self.file_storage.enabled && !self.database_storage.enabled {
            return Err(anyhow::anyhow!("At least one storage type must be enabled"));
        }

        // 验证文件存储配置
        if self.file_storage.enabled {
            if self.file_storage.base_path.is_empty() {
                return Err(anyhow::anyhow!("File storage base path cannot be empty"));
            }
            if self.file_storage.max_file_size == 0 {
                return Err(anyhow::anyhow!("Max file size must be greater than 0"));
            }
        }

        // 验证数据库存储配置
        if self.database_storage.enabled {
            if self.database_storage.connection_string.is_empty() {
                return Err(anyhow::anyhow!("Database connection string cannot be empty"));
            }
            if self.database_storage.max_connections == 0 {
                return Err(anyhow::anyhow!("Max connections must be greater than 0"));
            }
        }

        // 验证路由规则
        if self.routing.rules.is_empty() {
            return Err(anyhow::anyhow!("At least one routing rule must be defined"));
        }

        // 验证保留策略
        for policy in &self.retention.policies {
            if policy.retention_days == 0 {
                return Err(anyhow::anyhow!("Retention days must be greater than 0 for policy: {}", policy.name));
            }
            if policy.archive_after_days > policy.retention_days {
                return Err(anyhow::anyhow!("Archive after days cannot be greater than retention days for policy: {}", policy.name));
            }
        }

        Ok(())
    }

    /// 获取默认存储类型
    pub fn get_default_storage(&self) -> &str {
        &self.global.default_storage
    }

    /// 检查是否启用备份
    pub fn is_backup_enabled(&self) -> bool {
        self.global.backup_enabled && self.backup.enabled
    }

    /// 检查是否启用监控
    pub fn is_monitoring_enabled(&self) -> bool {
        self.global.monitoring_enabled && self.monitoring.enabled
    }

    /// 获取批处理大小
    pub fn get_batch_size(&self) -> usize {
        self.performance.batch_size
    }

    /// 获取连接池大小
    pub fn get_connection_pool_size(&self) -> u32 {
        self.performance.connection_pool_size
    }
}
```

## 7. 监控和统计

### 7.1 存储指标收集器

```rust
// storage/src/metrics.rs
use std::sync::atomic::{AtomicU64, AtomicF64, Ordering};
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};

pub struct StorageMetrics {
    // 基础计数器
    total_stored: AtomicU64,
    total_queried: AtomicU64,
    total_deleted: AtomicU64,
    storage_errors: AtomicU64,
    query_errors: AtomicU64,

    // 存储统计
    bytes_stored: AtomicU64,
    bytes_queried: AtomicU64,
    average_record_size: AtomicF64,

    // 性能统计
    total_storage_time: AtomicU64,
    total_query_time: AtomicU64,
    min_storage_time: AtomicU64,
    max_storage_time: AtomicU64,
    min_query_time: AtomicU64,
    max_query_time: AtomicU64,

    // 分类统计
    storage_type_stats: Arc<Mutex<HashMap<String, StorageTypeMetrics>>>,
    table_stats: Arc<Mutex<HashMap<String, TableMetrics>>>,

    // 时间统计
    start_time: DateTime<Utc>,
    last_reset: Arc<Mutex<DateTime<Utc>>>,
}

#[derive(Debug, Clone, Default)]
pub struct StorageTypeMetrics {
    pub stored_count: u64,
    pub queried_count: u64,
    pub deleted_count: u64,
    pub error_count: u64,
    pub bytes_stored: u64,
    pub avg_storage_time: f64,
    pub avg_query_time: f64,
}

#[derive(Debug, Clone, Default)]
pub struct TableMetrics {
    pub record_count: u64,
    pub total_size: u64,
    pub last_access: DateTime<Utc>,
    pub access_frequency: u64,
}

impl StorageMetrics {
    pub fn new() -> Self {
        Self {
            total_stored: AtomicU64::new(0),
            total_queried: AtomicU64::new(0),
            total_deleted: AtomicU64::new(0),
            storage_errors: AtomicU64::new(0),
            query_errors: AtomicU64::new(0),
            bytes_stored: AtomicU64::new(0),
            bytes_queried: AtomicU64::new(0),
            average_record_size: AtomicF64::new(0.0),
            total_storage_time: AtomicU64::new(0),
            total_query_time: AtomicU64::new(0),
            min_storage_time: AtomicU64::new(u64::MAX),
            max_storage_time: AtomicU64::new(0),
            min_query_time: AtomicU64::new(u64::MAX),
            max_query_time: AtomicU64::new(0),
            storage_type_stats: Arc::new(Mutex::new(HashMap::new())),
            table_stats: Arc::new(Mutex::new(HashMap::new())),
            start_time: Utc::now(),
            last_reset: Arc::new(Mutex::new(Utc::now())),
        }
    }

    /// 记录存储操作
    pub async fn record_storage(&self, storage_type: String, bytes: usize, success: bool) {
        self.total_stored.fetch_add(1, Ordering::Relaxed);

        if success {
            self.bytes_stored.fetch_add(bytes as u64, Ordering::Relaxed);

            // 更新平均记录大小
            let total_records = self.total_stored.load(Ordering::Relaxed);
            let total_bytes = self.bytes_stored.load(Ordering::Relaxed);
            let avg_size = total_bytes as f64 / total_records as f64;
            self.average_record_size.store(avg_size, Ordering::Relaxed);
        } else {
            self.storage_errors.fetch_add(1, Ordering::Relaxed);
        }

        // 更新存储类型统计
        let mut type_stats = self.storage_type_stats.lock().await;
        let entry = type_stats.entry(storage_type).or_default();
        entry.stored_count += 1;
        if success {
            entry.bytes_stored += bytes as u64;
        } else {
            entry.error_count += 1;
        }
    }

    /// 记录查询操作
    pub async fn record_query(&self, storage_type: String, result_count: usize, bytes_read: usize, query_time_ms: u64, success: bool) {
        self.total_queried.fetch_add(1, Ordering::Relaxed);
        self.total_query_time.fetch_add(query_time_ms, Ordering::Relaxed);

        if success {
            self.bytes_queried.fetch_add(bytes_read as u64, Ordering::Relaxed);

            // 更新最小/最大查询时间
            self.update_min_max_query_time(query_time_ms);
        } else {
            self.query_errors.fetch_add(1, Ordering::Relaxed);
        }

        // 更新存储类型统计
        let mut type_stats = self.storage_type_stats.lock().await;
        let entry = type_stats.entry(storage_type).or_default();
        entry.queried_count += 1;
        if success {
            let total_queries = entry.queried_count;
            entry.avg_query_time = (entry.avg_query_time * (total_queries - 1) as f64 + query_time_ms as f64) / total_queries as f64;
        } else {
            entry.error_count += 1;
        }
    }

    /// 记录删除操作
    pub async fn record_deletion(&self, storage_type: String, deleted_count: u64, success: bool) {
        self.total_deleted.fetch_add(deleted_count, Ordering::Relaxed);

        if !success {
            self.storage_errors.fetch_add(1, Ordering::Relaxed);
        }

        // 更新存储类型统计
        let mut type_stats = self.storage_type_stats.lock().await;
        let entry = type_stats.entry(storage_type).or_default();
        entry.deleted_count += deleted_count;
        if !success {
            entry.error_count += 1;
        }
    }

    /// 更新存储统计信息
    pub async fn update_storage_statistics(&self, storage_name: String, stats: StorageStatistics) {
        let mut table_stats = self.table_stats.lock().await;
        let entry = table_stats.entry(storage_name).or_default();
        entry.record_count = stats.total_records;
        entry.total_size = stats.total_size_bytes;
        entry.last_access = Utc::now();
        entry.access_frequency += 1;
    }

    /// 获取总体统计信息
    pub async fn get_overall_statistics(&self) -> OverallStorageStatistics {
        let total_stored = self.total_stored.load(Ordering::Relaxed);
        let total_queried = self.total_queried.load(Ordering::Relaxed);
        let total_deleted = self.total_deleted.load(Ordering::Relaxed);
        let storage_errors = self.storage_errors.load(Ordering::Relaxed);
        let query_errors = self.query_errors.load(Ordering::Relaxed);
        let bytes_stored = self.bytes_stored.load(Ordering::Relaxed);
        let bytes_queried = self.bytes_queried.load(Ordering::Relaxed);
        let total_storage_time = self.total_storage_time.load(Ordering::Relaxed);
        let total_query_time = self.total_query_time.load(Ordering::Relaxed);

        let storage_success_rate = if total_stored > 0 {
            ((total_stored - storage_errors) as f64 / total_stored as f64) * 100.0
        } else {
            0.0
        };

        let query_success_rate = if total_queried > 0 {
            ((total_queried - query_errors) as f64 / total_queried as f64) * 100.0
        } else {
            0.0
        };

        let avg_storage_time = if total_stored > 0 {
            total_storage_time as f64 / total_stored as f64
        } else {
            0.0
        };

        let avg_query_time = if total_queried > 0 {
            total_query_time as f64 / total_queried as f64
        } else {
            0.0
        };

        let uptime = Utc::now().signed_duration_since(self.start_time);

        OverallStorageStatistics {
            total_stored,
            total_queried,
            total_deleted,
            storage_errors,
            query_errors,
            bytes_stored,
            bytes_queried,
            storage_success_rate,
            query_success_rate,
            avg_storage_time_ms: avg_storage_time,
            avg_query_time_ms: avg_query_time,
            min_storage_time_ms: self.min_storage_time.load(Ordering::Relaxed),
            max_storage_time_ms: self.max_storage_time.load(Ordering::Relaxed),
            min_query_time_ms: self.min_query_time.load(Ordering::Relaxed),
            max_query_time_ms: self.max_query_time.load(Ordering::Relaxed),
            average_record_size: self.average_record_size.load(Ordering::Relaxed),
            uptime_seconds: uptime.num_seconds() as u64,
        }
    }

    /// 获取存储类型统计
    pub async fn get_storage_type_statistics(&self) -> HashMap<String, StorageTypeMetrics> {
        self.storage_type_stats.lock().await.clone()
    }

    /// 获取表统计信息
    pub async fn get_table_statistics(&self) -> HashMap<String, TableMetrics> {
        self.table_stats.lock().await.clone()
    }

    /// 重置统计信息
    pub async fn reset_statistics(&self) {
        self.total_stored.store(0, Ordering::Relaxed);
        self.total_queried.store(0, Ordering::Relaxed);
        self.total_deleted.store(0, Ordering::Relaxed);
        self.storage_errors.store(0, Ordering::Relaxed);
        self.query_errors.store(0, Ordering::Relaxed);
        self.bytes_stored.store(0, Ordering::Relaxed);
        self.bytes_queried.store(0, Ordering::Relaxed);
        self.total_storage_time.store(0, Ordering::Relaxed);
        self.total_query_time.store(0, Ordering::Relaxed);
        self.min_storage_time.store(u64::MAX, Ordering::Relaxed);
        self.max_storage_time.store(0, Ordering::Relaxed);
        self.min_query_time.store(u64::MAX, Ordering::Relaxed);
        self.max_query_time.store(0, Ordering::Relaxed);
        self.average_record_size.store(0.0, Ordering::Relaxed);

        self.storage_type_stats.lock().await.clear();
        self.table_stats.lock().await.clear();

        *self.last_reset.lock().await = Utc::now();
    }

    fn update_min_max_query_time(&self, query_time_ms: u64) {
        // 更新最小查询时间
        let mut current_min = self.min_query_time.load(Ordering::Relaxed);
        while query_time_ms < current_min {
            match self.min_query_time.compare_exchange_weak(
                current_min,
                query_time_ms,
                Ordering::Relaxed,
                Ordering::Relaxed,
            ) {
                Ok(_) => break,
                Err(x) => current_min = x,
            }
        }

        // 更新最大查询时间
        let mut current_max = self.max_query_time.load(Ordering::Relaxed);
        while query_time_ms > current_max {
            match self.max_query_time.compare_exchange_weak(
                current_max,
                query_time_ms,
                Ordering::Relaxed,
                Ordering::Relaxed,
            ) {
                Ok(_) => break,
                Err(x) => current_max = x,
            }
        }
    }
}

#[derive(Debug, Clone)]
pub struct OverallStorageStatistics {
    pub total_stored: u64,
    pub total_queried: u64,
    pub total_deleted: u64,
    pub storage_errors: u64,
    pub query_errors: u64,
    pub bytes_stored: u64,
    pub bytes_queried: u64,
    pub storage_success_rate: f64,
    pub query_success_rate: f64,
    pub avg_storage_time_ms: f64,
    pub avg_query_time_ms: f64,
    pub min_storage_time_ms: u64,
    pub max_storage_time_ms: u64,
    pub min_query_time_ms: u64,
    pub max_query_time_ms: u64,
    pub average_record_size: f64,
    pub uptime_seconds: u64,
}
```

## 8. 性能优化

### 8.1 存储性能优化策略

1. **批量操作优化**
   - 批量插入：减少数据库连接开销
   - 批量查询：合并多个查询请求
   - 批量删除：提高删除操作效率

2. **索引优化**
   - 时间索引：基于timestamp字段的B-tree索引
   - 复合索引：gantry_code + timestamp复合索引
   - 车牌号索引：支持车牌号快速查询
   - 全文索引：支持模糊搜索

3. **分区优化**
   - 时间分区：按日/月分区，提高查询效率
   - 门架分区：按门架代码分区，支持并行处理
   - 混合分区：结合时间和门架的混合分区策略

4. **缓存优化**
   - 查询结果缓存：缓存频繁查询的结果
   - 元数据缓存：缓存表结构和索引信息
   - 连接池缓存：复用数据库连接

5. **压缩优化**
   - 数据压缩：对历史数据进行压缩存储
   - 图像压缩：对图像数据进行有损/无损压缩
   - 传输压缩：网络传输时的数据压缩

## 9. 错误处理和恢复

### 9.1 错误处理策略

| 错误类型 | 处理策略 | 恢复机制 | 重试次数 |
|---------|---------|---------|---------|
| 磁盘空间不足 | 自动清理过期数据 | 扩展存储空间 | 0次 |
| 数据库连接失败 | 重新建立连接 | 切换备用数据库 | 3次 |
| 文件写入失败 | 切换存储位置 | 修复文件权限 | 2次 |
| 数据损坏 | 从备份恢复 | 数据校验和修复 | 1次 |
| 查询超时 | 优化查询语句 | 增加超时时间 | 2次 |
| 索引损坏 | 重建索引 | 使用备用索引 | 1次 |

### 9.2 数据恢复机制

```rust
// storage/src/recovery.rs
pub struct DataRecoveryManager {
    backup_locations: Vec<String>,
    recovery_strategies: Vec<RecoveryStrategy>,
}

#[derive(Debug, Clone)]
pub enum RecoveryStrategy {
    BackupRestore,
    ReplicationSync,
    PartialRecovery,
    ManualIntervention,
}

impl DataRecoveryManager {
    /// 执行数据恢复
    pub async fn recover_data(&self, recovery_request: RecoveryRequest) -> Result<RecoveryResult> {
        for strategy in &self.recovery_strategies {
            match strategy {
                RecoveryStrategy::BackupRestore => {
                    if let Ok(result) = self.restore_from_backup(&recovery_request).await {
                        return Ok(result);
                    }
                }
                RecoveryStrategy::ReplicationSync => {
                    if let Ok(result) = self.sync_from_replica(&recovery_request).await {
                        return Ok(result);
                    }
                }
                _ => continue,
            }
        }

        Err(anyhow::anyhow!("All recovery strategies failed"))
    }

    async fn restore_from_backup(&self, request: &RecoveryRequest) -> Result<RecoveryResult> {
        // 实现备份恢复逻辑
        Ok(RecoveryResult {
            success: true,
            recovered_records: 0,
            recovery_time_ms: 0,
        })
    }

    async fn sync_from_replica(&self, request: &RecoveryRequest) -> Result<RecoveryResult> {
        // 实现副本同步逻辑
        Ok(RecoveryResult {
            success: true,
            recovered_records: 0,
            recovery_time_ms: 0,
        })
    }
}

#[derive(Debug, Clone)]
pub struct RecoveryRequest {
    pub data_range: TimeRange,
    pub storage_type: String,
    pub priority: RecoveryPriority,
}

#[derive(Debug, Clone)]
pub enum RecoveryPriority {
    Critical,
    High,
    Normal,
    Low,
}

#[derive(Debug, Clone)]
pub struct RecoveryResult {
    pub success: bool,
    pub recovered_records: u64,
    pub recovery_time_ms: u64,
}
```

## 10. 总结

数据存储模块是数据采集系统的核心持久化组件，负责确保数据的安全存储和高效检索。本设计文档详细描述了：

1. **多存储支持**: 支持本地文件存储和数据库存储，满足不同场景需求
2. **智能路由**: 基于数据类型、大小、时间等条件的智能存储路由
3. **分区策略**: 支持时间分区、门架分区、大小分区等多种分区策略
4. **数据生命周期**: 完整的数据保留、归档、清理生命周期管理
5. **备份恢复**: 全面的数据备份和灾难恢复机制
6. **性能优化**: 批量操作、索引优化、缓存机制等性能优化策略
7. **监控统计**: 全面的存储指标收集和性能监控

该设计确保了数据存储的高可靠性、高性能和高可用性，为整个数据采集系统提供了坚实的数据持久化基础。通过灵活的配置和扩展机制，可以根据实际需求调整存储策略和性能参数。
