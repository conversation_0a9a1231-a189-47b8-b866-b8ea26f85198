# DDD-001 系统总体架构详细设计文档

**文档编号：** DDD-001  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 系统架构团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md

---

## 1. 文档概述

### 1.1 文档目的
本文档基于PRD-001需求文档，详细描述高速路门架数据采集系统的总体架构设计，为各模块的详细设计提供指导和约束。

### 1.2 需求追溯
本设计文档对应PRD-001中的以下需求：
- **FR-001**: 第三方应用日志采集
- **FR-002**: 摄像头数据采集  
- **FR-003**: 数据传输功能
- **NFR-001**: 性能要求（≥1000条/秒，≥10路摄像头）
- **NFR-002**: 可靠性要求（≥99.9%可用性）
- **NFR-003**: 安全要求（TLS/SSL加密）

### 1.3 架构原则
- **模块化设计**: 采用Cargo Workspace多crate架构，职责清晰
- **高可用性**: 支持故障自动恢复，数据不丢失
- **可扩展性**: 支持水平扩展和新数据源接入
- **高性能**: 异步处理，支持高并发数据采集
- **安全性**: 数据传输加密，访问权限控制

## 2. 系统总体架构

### 2.1 架构概览

```mermaid
graph TB
    subgraph "外部数据源"
        DS1[第三方应用日志]
        DS2[SFTP服务器]
        DS3[摄像头SDK]
    end

    subgraph "数据采集层"
        subgraph "Transaction Crate"
            TC1[被动日志采集器]
            TC2[SFTP主动采集器]
        end
        subgraph "Image Crate"
            IC1[摄像头数据采集器]
            IC2[SDK管理器]
        end
        subgraph "Types Crate"
            TY1[数据类型定义]
            TY2[通用Trait]
        end
    end

    subgraph "数据处理层"
        DP1[数据归一化处理器]
        DP2[数据验证器]
        DP3[格式转换器]
    end

    subgraph "数据传输层"
        subgraph "Transport Crate"
            TR1[串口传输器]
            TR2[TCP传输器]
            TR3[传输管理器]
        end
    end

    subgraph "数据存储层"
        subgraph "Storage Crate"
            ST1[本地文件存储]
            ST2[数据库存储]
            ST3[存储管理器]
        end
    end

    subgraph "系统管理层"
        subgraph "HCS Main App"
            HCS1[配置管理]
            HCS2[系统协调器]
            HCS3[监控告警]
            HCS4[健康检查]
        end
    end

    subgraph "外部系统"
        EXT1[外部设备<br/>串口连接]
        EXT2[远程服务器<br/>网络连接]
        EXT3[监控系统]
    end

    DS1 --> TC1
    DS2 --> TC2
    DS3 --> IC1

    TC1 --> DP1
    TC2 --> DP1
    IC1 --> DP1
    
    TY1 -.-> TC1
    TY1 -.-> TC2
    TY1 -.-> IC1
    TY2 -.-> DP1

    DP1 --> DP2
    DP2 --> DP3

    DP3 --> TR1
    DP3 --> TR2
    DP3 --> ST1

    TR1 --> EXT1
    TR2 --> EXT2
    ST1 --> ST2

    HCS1 -.-> TC1
    HCS1 -.-> TC2
    HCS1 -.-> IC1
    HCS1 -.-> TR3
    HCS1 -.-> ST3
    HCS2 -.-> DP1
    HCS3 -.-> EXT3
```

### 2.2 分层架构说明

#### 2.2.1 外部数据源层
- **第三方应用日志**: 交易日志文件，包含车辆通行信息
- **SFTP服务器**: 远程日志文件存储服务器
- **摄像头SDK**: 摄像头厂商提供的数据采集接口

#### 2.2.2 数据采集层
- **Transaction Crate**: 负责交易日志数据采集
  - 被动采集：监控本地目录文件变化
  - 主动采集：定时从SFTP服务器下载
- **Image Crate**: 负责摄像头数据采集
  - SDK封装和设备管理
  - 实时数据流处理
- **Types Crate**: 提供通用数据类型和接口定义

#### 2.2.3 数据处理层
- **数据归一化处理器**: 将不同来源数据转换为统一格式
- **数据验证器**: 验证数据完整性和正确性
- **格式转换器**: 根据传输需求转换数据格式

#### 2.2.4 数据传输层
- **Transport Crate**: 提供多种数据传输方式
  - 串口传输：RS232/RS485协议
  - TCP传输：网络协议传输
  - 传输管理：连接管理和故障恢复

#### 2.2.5 数据存储层
- **Storage Crate**: 提供数据持久化功能
  - 本地文件存储：高性能本地缓存
  - 数据库存储：结构化数据管理
  - 存储管理：存储策略和生命周期管理

#### 2.2.6 系统管理层
- **HCS Main App**: 系统主应用程序
  - 配置管理：统一配置文件管理
  - 系统协调器：模块间协调和调度
  - 监控告警：系统状态监控和异常告警
  - 健康检查：系统健康状态检查

## 3. 技术架构

### 3.1 技术选型

| 技术领域 | 选型 | 版本 | 选型理由 |
|---------|------|------|---------|
| 编程语言 | Rust | 1.70+ | 内存安全、高性能、并发支持 |
| 项目结构 | Cargo Workspace | - | 模块化管理，依赖隔离 |
| 异步运行时 | Tokio | 1.0+ | 成熟的异步运行时，生态丰富 |
| 序列化 | Serde | 1.0+ | 高性能序列化框架 |
| 日志框架 | Tracing | 0.1+ | 结构化日志，支持分布式追踪 |
| 错误处理 | Anyhow | 1.0+ | 简化错误处理，错误链追踪 |
| 时间处理 | Chrono | 0.4+ | 全面的时间处理库 |
| 配置管理 | Config | 0.13+ | 多格式配置文件支持 |
| 数据库 | SQLite/PostgreSQL | - | 轻量级/企业级数据库选择 |

### 3.2 Crate依赖关系

```mermaid
graph TD
    subgraph "应用层"
        HCS[hcs]
    end
    
    subgraph "业务层"
        TRANS[transaction]
        IMAGE[image]
        TRANSPORT[transport]
        STORAGE[storage]
    end
    
    subgraph "基础层"
        TYPES[types]
    end
    
    subgraph "外部依赖"
        TOKIO[tokio]
        SERDE[serde]
        ANYHOW[anyhow]
        TRACING[tracing]
        CHRONO[chrono]
    end

    HCS --> TRANS
    HCS --> IMAGE
    HCS --> TRANSPORT
    HCS --> STORAGE
    
    TRANS --> TYPES
    IMAGE --> TYPES
    TRANSPORT --> TYPES
    STORAGE --> TYPES
    
    TYPES --> SERDE
    TYPES --> CHRONO
    
    HCS --> TOKIO
    HCS --> ANYHOW
    HCS --> TRACING
    
    TRANS --> TOKIO
    IMAGE --> TOKIO
    TRANSPORT --> TOKIO
    STORAGE --> TOKIO
```

### 3.3 核心Crate职责

#### 3.3.1 types Crate
- **职责**: 定义系统通用数据类型和接口
- **主要内容**:
  - `Transaction`: 交易数据结构
  - `VehicleData`: 摄像头数据结构
  - `NormalizedData`: 归一化数据结构
  - `Collector` trait: 数据采集器接口
  - 错误类型定义

#### 3.3.2 transaction Crate
- **职责**: 交易日志数据采集
- **主要内容**:
  - 文件监控采集器
  - SFTP客户端采集器
  - 日志解析器
  - 数据格式转换

#### 3.3.3 image Crate
- **职责**: 摄像头数据采集
- **主要内容**:
  - SDK封装和管理
  - 设备连接管理
  - 实时数据流处理
  - 图像数据处理

#### 3.3.4 transport Crate
- **职责**: 数据传输功能
- **主要内容**:
  - 串口通信实现
  - TCP/UDP网络通信
  - 传输协议定义
  - 连接管理和重连

#### 3.3.5 storage Crate
- **职责**: 数据存储管理
- **主要内容**:
  - 本地文件存储
  - 数据库操作
  - 存储策略管理
  - 数据生命周期管理

#### 3.3.6 hcs Crate
- **职责**: 主应用程序
- **主要内容**:
  - 系统启动和初始化
  - 配置文件管理
  - 模块协调和调度
  - 监控和健康检查

## 4. 数据流架构

### 4.1 数据流概览

```mermaid
flowchart TD
    A[数据源] --> B{数据类型}
    B -->|交易日志| C[Transaction采集]
    B -->|摄像头数据| D[Image采集]
    
    C --> E[数据归一化]
    D --> E
    
    E --> F[数据验证]
    F --> G{验证结果}
    G -->|通过| H[格式转换]
    G -->|失败| I[异常处理]
    
    H --> J{传输策略}
    J -->|串口| K[串口传输]
    J -->|网络| L[TCP传输]
    J -->|存储| M[本地存储]
    
    K --> N[外部设备]
    L --> O[远程服务器]
    M --> P[数据库]
    
    I --> Q[错误日志]
    Q --> R[监控告警]
```

### 4.2 数据流详细说明

#### 4.2.1 数据采集阶段
1. **数据源配置**: 配置文件定义数据源类型和参数
2. **采集器初始化**: 根据配置初始化对应的采集器
3. **数据读取**: 采集器从数据源读取原始数据
4. **初步处理**: 进行基本的数据清洗和格式检查

#### 4.2.2 数据处理阶段
1. **数据归一化**: 将不同格式的数据转换为统一的`NormalizedData`格式
2. **数据验证**: 检查数据完整性、格式正确性和业务规则
3. **格式转换**: 根据传输目标的要求转换数据格式（JSON、二进制等）

#### 4.2.3 数据传输阶段
1. **传输策略选择**: 根据配置选择传输方式（串口、网络、存储）
2. **数据传输**: 通过选定的传输方式发送数据
3. **确认机制**: 等待接收方确认，处理传输失败情况

#### 4.2.4 异常处理流程
1. **异常检测**: 在各个阶段检测异常情况
2. **异常分类**: 根据异常类型选择处理策略
3. **恢复机制**: 执行自动恢复或人工干预
4. **日志记录**: 记录异常信息用于后续分析

## 5. 部署架构

### 5.1 物理部署架构

```mermaid
graph TB
    subgraph "门架现场"
        subgraph "主节点"
            M1[HCS主应用]
            M2[数据采集服务]
            M3[本地存储]
        end

        subgraph "备份节点"
            B1[HCS备份应用]
            B2[备份采集服务]
        end

        subgraph "外部设备"
            E1[摄像头设备]
            E2[串口设备]
            E3[第三方应用]
        end
    end

    subgraph "数据中心"
        subgraph "接收服务器"
            R1[数据接收服务]
            R2[数据处理服务]
            R3[数据库服务]
        end

        subgraph "监控中心"
            MON1[监控服务]
            MON2[告警服务]
            MON3[仪表板]
        end
    end

    E1 --> M2
    E2 --> M2
    E3 --> M2

    M1 -.->|故障切换| B1
    M2 -.->|故障切换| B2

    M1 -->|网络传输| R1
    M3 -->|数据同步| R3

    M1 -->|监控数据| MON1
    MON1 --> MON2
    MON2 --> MON3
```

### 5.2 逻辑部署架构

```mermaid
graph TB
    subgraph "应用层"
        APP1[配置管理服务]
        APP2[系统协调服务]
        APP3[监控告警服务]
    end

    subgraph "服务层"
        SVC1[数据采集服务]
        SVC2[数据处理服务]
        SVC3[数据传输服务]
        SVC4[数据存储服务]
    end

    subgraph "基础设施层"
        INF1[操作系统<br/>Linux]
        INF2[运行时<br/>Tokio]
        INF3[网络<br/>TCP/IP]
        INF4[存储<br/>文件系统]
    end

    APP1 --> SVC1
    APP2 --> SVC2
    APP3 --> SVC3

    SVC1 --> INF2
    SVC2 --> INF2
    SVC3 --> INF3
    SVC4 --> INF4

    INF2 --> INF1
    INF3 --> INF1
    INF4 --> INF1
```

## 6. 性能设计

### 6.1 性能目标

基于PRD-001的性能要求，系统需要满足以下性能指标：

| 性能指标 | 目标值 | 设计策略 |
|---------|--------|---------|
| 数据处理吞吐量 | ≥1000条/秒 | 异步处理、批量操作 |
| 摄像头并发数 | ≥10路 | 多线程并发、连接池 |
| 数据处理延迟 | ≤100ms | 内存缓存、流式处理 |
| 系统响应时间 | ≤1秒 | 异步I/O、非阻塞操作 |
| 内存使用 | ≤2GB | 内存池、数据流式处理 |
| CPU使用率 | ≤70% | 负载均衡、任务调度 |

### 6.2 性能优化策略

#### 6.2.1 数据采集优化
- **批量处理**: 批量读取和处理数据，减少I/O开销
- **异步I/O**: 使用Tokio异步运行时，提高并发性能
- **内存映射**: 对大文件使用内存映射技术
- **连接复用**: 复用网络连接，减少连接建立开销

#### 6.2.2 数据处理优化
- **流式处理**: 采用流式处理模式，避免大量数据在内存中积累
- **并行处理**: 利用多核CPU进行并行数据处理
- **缓存机制**: 对频繁访问的数据进行缓存
- **零拷贝**: 在数据传输过程中尽量避免不必要的内存拷贝

#### 6.2.3 存储优化
- **分区存储**: 按时间或数据类型分区存储
- **压缩算法**: 对历史数据进行压缩存储
- **索引优化**: 建立合适的数据库索引
- **读写分离**: 分离读写操作，提高并发性能

## 7. 可靠性设计

### 7.1 可靠性目标

基于PRD-001的可靠性要求：

| 可靠性指标 | 目标值 | 设计策略 |
|-----------|--------|---------|
| 系统可用性 | ≥99.9% | 冗余设计、故障切换 |
| 数据完整性 | 数据丢失率≤0.01% | 数据校验、备份机制 |
| 故障恢复时间 | ≤5分钟 | 自动故障检测和恢复 |
| 平均故障间隔 | ≥720小时 | 健壮性设计、预防性维护 |

### 7.2 可靠性保障机制

#### 7.2.1 故障检测
- **健康检查**: 定期检查各模块运行状态
- **心跳机制**: 监控关键服务的心跳信号
- **异常监控**: 实时监控系统异常和错误
- **性能监控**: 监控系统性能指标变化

#### 7.2.2 故障恢复
- **自动重启**: 检测到故障时自动重启服务
- **故障切换**: 主备节点间的自动故障切换
- **数据恢复**: 从备份数据中恢复丢失的数据
- **服务降级**: 在部分功能故障时提供基本服务

#### 7.2.3 数据保护
- **数据校验**: 使用校验和验证数据完整性
- **实时备份**: 实时备份关键数据
- **版本控制**: 保留数据的多个版本
- **异地备份**: 在不同位置保存数据备份

## 8. 安全设计

### 8.1 安全目标

基于PRD-001的安全要求：

| 安全领域 | 要求 | 实现方案 |
|---------|------|---------|
| 数据传输安全 | TLS/SSL加密 | 使用TLS 1.3协议 |
| 访问控制 | 基于角色的权限管理 | RBAC权限模型 |
| 数据存储安全 | 敏感数据加密 | AES-256加密算法 |
| 审计日志 | 完整的操作记录 | 结构化审计日志 |

### 8.2 安全架构

```mermaid
graph TB
    subgraph "安全边界"
        subgraph "应用安全层"
            AS1[身份认证]
            AS2[权限控制]
            AS3[输入验证]
        end

        subgraph "传输安全层"
            TS1[TLS加密]
            TS2[证书管理]
            TS3[密钥管理]
        end

        subgraph "存储安全层"
            SS1[数据加密]
            SS2[访问控制]
            SS3[备份加密]
        end

        subgraph "监控安全层"
            MS1[安全审计]
            MS2[异常检测]
            MS3[入侵检测]
        end
    end

    AS1 --> TS1
    AS2 --> SS2
    AS3 --> MS2

    TS1 --> SS1
    TS2 --> SS3
    TS3 --> MS1
```

## 9. 监控和运维

### 9.1 监控架构

```mermaid
graph TB
    subgraph "监控数据源"
        MD1[系统指标]
        MD2[业务指标]
        MD3[日志数据]
        MD4[性能数据]
    end

    subgraph "数据收集层"
        DC1[指标收集器]
        DC2[日志收集器]
        DC3[追踪收集器]
    end

    subgraph "数据处理层"
        DP1[数据聚合]
        DP2[数据分析]
        DP3[告警规则]
    end

    subgraph "展示层"
        DIS1[监控仪表板]
        DIS2[告警通知]
        DIS3[报表系统]
    end

    MD1 --> DC1
    MD2 --> DC1
    MD3 --> DC2
    MD4 --> DC3

    DC1 --> DP1
    DC2 --> DP2
    DC3 --> DP3

    DP1 --> DIS1
    DP2 --> DIS2
    DP3 --> DIS3
```

### 9.2 运维策略

#### 9.2.1 部署策略
- **蓝绿部署**: 零停机时间部署
- **滚动更新**: 逐步更新服务实例
- **回滚机制**: 快速回滚到上一个稳定版本
- **配置管理**: 统一的配置管理和分发

#### 9.2.2 维护策略
- **预防性维护**: 定期系统检查和优化
- **容量规划**: 根据业务增长规划系统容量
- **性能调优**: 持续的性能监控和优化
- **安全更新**: 及时应用安全补丁和更新

## 10. 总结

本文档详细描述了高速路门架数据采集系统的总体架构设计，包括：

1. **分层架构**: 采用清晰的分层架构，职责明确，便于维护和扩展
2. **模块化设计**: 基于Cargo Workspace的模块化设计，支持独立开发和测试
3. **高性能**: 通过异步处理、批量操作等策略满足性能要求
4. **高可靠性**: 通过冗余设计、故障切换等机制保证系统可靠性
5. **安全性**: 全面的安全设计，保护数据传输和存储安全
6. **可运维性**: 完善的监控和运维体系，支持系统的长期稳定运行

该架构设计为后续各模块的详细设计提供了指导框架，确保整个系统的一致性和完整性。
