# DDD-003 数据处理模块详细设计文档

**文档编号：** DDD-003  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 数据处理团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md  
**上级设计：** DDD-001-系统总体架构详细设计文档.md

---

## 1. 模块概述

### 1.1 模块目的
数据处理模块负责对采集到的原始数据进行归一化、验证、清洗和格式转换，确保数据质量和格式的统一性，为后续的传输和存储提供标准化的数据。

### 1.2 需求追溯
本模块对应PRD-001中的以下需求：
- **数据预处理和格式化**: 将不同来源的数据转换为统一格式
- **数据质量保障**: 确保采集数据的完整性和准确性
- **数据验证**: 检查数据格式正确性和业务规则符合性
- **性能要求**: 数据处理延迟≤100ms，支持≥1000条/秒处理能力

### 1.3 模块职责
- **数据归一化**: 将Transaction和VehicleData转换为统一的NormalizedData格式
- **数据验证**: 验证数据完整性、格式正确性和业务规则
- **数据清洗**: 处理缺失值、异常值和重复数据
- **格式转换**: 根据传输目标需求转换数据格式
- **数据增强**: 添加元数据、计算衍生字段

## 2. 模块架构设计

### 2.1 处理流程架构

```mermaid
flowchart TD
    A[原始数据输入] --> B{数据类型识别}
    B -->|Transaction| C[交易数据归一化器]
    B -->|VehicleData| D[摄像头数据归一化器]
    
    C --> E[数据归一化处理器]
    D --> E
    
    E --> F[数据验证器]
    F --> G{验证结果}
    G -->|通过| H[数据清洗器]
    G -->|失败| I[异常数据处理器]
    
    H --> J[数据增强器]
    J --> K[格式转换器]
    
    K --> L{输出格式}
    L -->|JSON| M[JSON序列化器]
    L -->|Binary| N[二进制序列化器]
    L -->|Custom| O[自定义格式器]
    
    M --> P[处理完成数据]
    N --> P
    O --> P
    
    I --> Q[错误日志记录]
    Q --> R[监控告警]
```

### 2.2 组件架构图

```mermaid
graph TB
    subgraph "数据处理模块"
        subgraph "归一化层"
            N1[TransactionNormalizer<br/>交易数据归一化器]
            N2[VehicleDataNormalizer<br/>摄像头数据归一化器]
            N3[DataNormalizer<br/>统一归一化处理器]
        end
        
        subgraph "验证层"
            V1[SchemaValidator<br/>模式验证器]
            V2[BusinessValidator<br/>业务规则验证器]
            V3[DataValidator<br/>统一验证器]
        end
        
        subgraph "清洗层"
            C1[MissingValueHandler<br/>缺失值处理器]
            C2[OutlierDetector<br/>异常值检测器]
            C3[DuplicateRemover<br/>重复数据移除器]
            C4[DataCleaner<br/>统一清洗器]
        end
        
        subgraph "增强层"
            E1[MetadataEnhancer<br/>元数据增强器]
            E2[DerivedFieldCalculator<br/>衍生字段计算器]
            E3[DataEnhancer<br/>统一增强器]
        end
        
        subgraph "转换层"
            T1[JsonFormatter<br/>JSON格式器]
            T2[BinaryFormatter<br/>二进制格式器]
            T3[CustomFormatter<br/>自定义格式器]
            T4[FormatConverter<br/>格式转换器]
        end
    end
    
    subgraph "外部依赖"
        EXT1[Types Crate<br/>数据类型定义]
        EXT2[Config<br/>配置管理]
        EXT3[Metrics<br/>指标收集]
    end

    N1 --> N3
    N2 --> N3
    N3 --> V3
    
    V1 --> V3
    V2 --> V3
    V3 --> C4
    
    C1 --> C4
    C2 --> C4
    C3 --> C4
    C4 --> E3
    
    E1 --> E3
    E2 --> E3
    E3 --> T4
    
    T1 --> T4
    T2 --> T4
    T3 --> T4
    
    EXT1 -.-> N3
    EXT1 -.-> V3
    EXT2 -.-> V3
    EXT2 -.-> C4
    EXT3 -.-> T4
```

## 3. 核心接口设计

### 3.1 数据处理器接口

```rust
// 数据处理器统一接口
use anyhow::Result;
use types::NormalizedData;

pub trait DataProcessor: Send + Sync {
    /// 处理单条数据
    async fn process(&mut self, data: NormalizedData) -> Result<ProcessedData>;
    
    /// 批量处理数据
    async fn process_batch(&mut self, data: Vec<NormalizedData>) -> Result<Vec<ProcessedData>>;
    
    /// 获取处理器状态
    fn get_status(&self) -> ProcessorStatus;
    
    /// 获取处理统计信息
    fn get_statistics(&self) -> ProcessorStatistics;
}

#[derive(Debug, Clone)]
pub struct ProcessedData {
    pub data: NormalizedData,
    pub metadata: ProcessingMetadata,
    pub format: OutputFormat,
    pub quality_score: f32,
}

#[derive(Debug, Clone)]
pub struct ProcessingMetadata {
    pub processed_at: u64,
    pub processing_time_ms: u64,
    pub processor_version: String,
    pub validation_results: Vec<ValidationResult>,
    pub cleaning_actions: Vec<CleaningAction>,
    pub enhancements: Vec<Enhancement>,
}

#[derive(Debug, Clone)]
pub enum OutputFormat {
    Json(String),
    Binary(Vec<u8>),
    Custom(String, Vec<u8>),
}

#[derive(Debug, Clone)]
pub enum ProcessorStatus {
    Idle,
    Processing,
    Error(String),
}

#[derive(Debug, Clone)]
pub struct ProcessorStatistics {
    pub total_processed: u64,
    pub success_count: u64,
    pub error_count: u64,
    pub average_processing_time_ms: f64,
    pub throughput_per_second: f64,
}
```

### 3.2 验证器接口

```rust
// 数据验证器接口
use anyhow::Result;
use types::NormalizedData;

pub trait DataValidator: Send + Sync {
    /// 验证单条数据
    async fn validate(&self, data: &NormalizedData) -> Result<ValidationResult>;
    
    /// 批量验证数据
    async fn validate_batch(&self, data: &[NormalizedData]) -> Result<Vec<ValidationResult>>;
    
    /// 获取验证规则
    fn get_rules(&self) -> Vec<ValidationRule>;
}

#[derive(Debug, Clone)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<ValidationWarning>,
    pub quality_score: f32,
}

#[derive(Debug, Clone)]
pub struct ValidationError {
    pub field: String,
    pub error_type: ValidationErrorType,
    pub message: String,
    pub severity: ErrorSeverity,
}

#[derive(Debug, Clone)]
pub enum ValidationErrorType {
    MissingRequired,
    InvalidFormat,
    OutOfRange,
    BusinessRuleViolation,
    DataInconsistency,
}

#[derive(Debug, Clone)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug, Clone)]
pub struct ValidationWarning {
    pub field: String,
    pub message: String,
    pub suggestion: Option<String>,
}

#[derive(Debug, Clone)]
pub struct ValidationRule {
    pub name: String,
    pub description: String,
    pub rule_type: RuleType,
    pub enabled: bool,
}

#[derive(Debug, Clone)]
pub enum RuleType {
    Schema,
    Business,
    Quality,
    Consistency,
}
```

## 4. 数据归一化处理器

### 4.1 功能描述
数据归一化处理器负责将不同来源的数据转换为统一的NormalizedData格式，确保数据结构的一致性。

### 4.2 实现设计

```rust
// 数据归一化处理器实现
use anyhow::{Result, anyhow};
use types::{Transaction, VehicleData, NormalizedData};
use uuid::Uuid;
use chrono::Utc;

pub struct DataNormalizer {
    gantry_code: String,
    default_values: DefaultValueConfig,
    field_mappings: FieldMappingConfig,
}

#[derive(Debug, Clone)]
pub struct DefaultValueConfig {
    pub default_vehicle_type: Option<String>,
    pub default_plate_color: Option<String>,
    pub quality_threshold: f32,
}

#[derive(Debug, Clone)]
pub struct FieldMappingConfig {
    pub vehicle_type_mapping: std::collections::HashMap<String, String>,
    pub plate_type_mapping: std::collections::HashMap<String, String>,
    pub fee_type_mapping: std::collections::HashMap<String, String>,
}

impl DataNormalizer {
    pub fn new(gantry_code: String, config: NormalizerConfig) -> Self {
        Self {
            gantry_code,
            default_values: config.default_values,
            field_mappings: config.field_mappings,
        }
    }

    /// 归一化交易数据
    pub async fn normalize_transaction(&self, transaction: Transaction) -> Result<NormalizedData> {
        let mut normalized = NormalizedData::from_transaction(
            self.gantry_code.clone(),
            transaction
        );

        // 应用字段映射
        self.apply_field_mappings(&mut normalized).await?;
        
        // 填充默认值
        self.apply_default_values(&mut normalized).await?;
        
        // 标准化字段格式
        self.standardize_fields(&mut normalized).await?;

        Ok(normalized)
    }

    /// 归一化摄像头数据
    pub async fn normalize_vehicle_data(&self, vehicle_data: VehicleData) -> Result<NormalizedData> {
        let mut normalized = NormalizedData::from_vehicle_data(
            self.gantry_code.clone(),
            vehicle_data
        );

        // 应用字段映射
        self.apply_field_mappings(&mut normalized).await?;
        
        // 填充默认值
        self.apply_default_values(&mut normalized).await?;
        
        // 标准化字段格式
        self.standardize_fields(&mut normalized).await?;

        Ok(normalized)
    }

    /// 应用字段映射
    async fn apply_field_mappings(&self, data: &mut NormalizedData) -> Result<()> {
        // 映射车辆类型
        if let Some(ref vehicle_type) = data.vehicle_type {
            if let Some(mapped_type) = self.field_mappings.vehicle_type_mapping.get(vehicle_type) {
                data.vehicle_type = Some(mapped_type.clone());
            }
        }

        // 映射车牌类型
        if let Some(ref plate_type) = data.plate_type {
            if let Some(mapped_type) = self.field_mappings.plate_type_mapping.get(plate_type) {
                data.plate_type = Some(mapped_type.clone());
            }
        }

        // 映射收费类型
        if let Some(ref fee_type) = data.vehicle_fee_type {
            if let Some(mapped_type) = self.field_mappings.fee_type_mapping.get(fee_type) {
                data.vehicle_fee_type = Some(mapped_type.clone());
            }
        }

        Ok(())
    }

    /// 应用默认值
    async fn apply_default_values(&self, data: &mut NormalizedData) -> Result<()> {
        // 设置默认车辆类型
        if data.vehicle_type.is_none() {
            data.vehicle_type = self.default_values.default_vehicle_type.clone();
        }

        // 设置默认车牌颜色
        if data.plate_color.is_none() {
            data.plate_color = self.default_values.default_plate_color.clone();
        }

        Ok(())
    }

    /// 标准化字段格式
    async fn standardize_fields(&self, data: &mut NormalizedData) -> Result<()> {
        // 标准化车牌号格式
        if let Some(ref mut plate_no) = data.plate_no {
            *plate_no = self.standardize_plate_number(plate_no)?;
        }

        // 标准化时间戳
        if data.timestamp == 0 {
            data.timestamp = Utc::now().timestamp_millis() as u64;
        }

        // 确保ID存在
        if data.id.is_empty() {
            data.id = Uuid::new_v4().to_string();
        }

        Ok(())
    }

    /// 标准化车牌号
    fn standardize_plate_number(&self, plate_no: &str) -> Result<String> {
        let cleaned = plate_no.trim().to_uppercase();
        
        // 验证车牌号格式
        if cleaned.len() < 7 || cleaned.len() > 8 {
            return Err(anyhow!("Invalid plate number length: {}", cleaned));
        }

        // 移除特殊字符
        let standardized = cleaned.chars()
            .filter(|c| c.is_alphanumeric())
            .collect::<String>();

        Ok(standardized)
    }
}
```

## 5. 数据验证器

### 5.1 统一验证器实现

```rust
// 统一数据验证器
use anyhow::Result;
use types::NormalizedData;
use regex::Regex;
use std::collections::HashMap;

pub struct DataValidator {
    schema_validator: SchemaValidator,
    business_validator: BusinessValidator,
    quality_validator: QualityValidator,
    enabled_rules: HashMap<String, bool>,
}

impl DataValidator {
    pub fn new(config: ValidatorConfig) -> Self {
        Self {
            schema_validator: SchemaValidator::new(config.schema_rules),
            business_validator: BusinessValidator::new(config.business_rules),
            quality_validator: QualityValidator::new(config.quality_rules),
            enabled_rules: config.enabled_rules,
        }
    }

    /// 验证数据
    pub async fn validate(&self, data: &NormalizedData) -> Result<ValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut quality_scores = Vec::new();

        // 模式验证
        if self.is_rule_enabled("schema_validation") {
            let schema_result = self.schema_validator.validate(data).await?;
            errors.extend(schema_result.errors);
            warnings.extend(schema_result.warnings);
            quality_scores.push(schema_result.quality_score);
        }

        // 业务规则验证
        if self.is_rule_enabled("business_validation") {
            let business_result = self.business_validator.validate(data).await?;
            errors.extend(business_result.errors);
            warnings.extend(business_result.warnings);
            quality_scores.push(business_result.quality_score);
        }

        // 质量验证
        if self.is_rule_enabled("quality_validation") {
            let quality_result = self.quality_validator.validate(data).await?;
            errors.extend(quality_result.errors);
            warnings.extend(quality_result.warnings);
            quality_scores.push(quality_result.quality_score);
        }

        let overall_quality = if quality_scores.is_empty() {
            1.0
        } else {
            quality_scores.iter().sum::<f32>() / quality_scores.len() as f32
        };

        Ok(ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
            quality_score: overall_quality,
        })
    }

    fn is_rule_enabled(&self, rule_name: &str) -> bool {
        self.enabled_rules.get(rule_name).copied().unwrap_or(true)
    }
}

/// 模式验证器
pub struct SchemaValidator {
    plate_regex: Regex,
    timestamp_range: (u64, u64),
    fee_range: (u32, u32),
}

impl SchemaValidator {
    pub fn new(config: SchemaValidationConfig) -> Self {
        Self {
            plate_regex: Regex::new(&config.plate_pattern).unwrap(),
            timestamp_range: config.timestamp_range,
            fee_range: config.fee_range,
        }
    }

    pub async fn validate(&self, data: &NormalizedData) -> Result<ValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut quality_score = 1.0f32;

        // 验证必填字段
        if data.id.is_empty() {
            errors.push(ValidationError {
                field: "id".to_string(),
                error_type: ValidationErrorType::MissingRequired,
                message: "ID field is required".to_string(),
                severity: ErrorSeverity::Critical,
            });
            quality_score -= 0.3;
        }

        if data.gantry_code.is_empty() {
            errors.push(ValidationError {
                field: "gantry_code".to_string(),
                error_type: ValidationErrorType::MissingRequired,
                message: "Gantry code is required".to_string(),
                severity: ErrorSeverity::Critical,
            });
            quality_score -= 0.3;
        }

        // 验证时间戳
        if data.timestamp < self.timestamp_range.0 || data.timestamp > self.timestamp_range.1 {
            errors.push(ValidationError {
                field: "timestamp".to_string(),
                error_type: ValidationErrorType::OutOfRange,
                message: format!("Timestamp {} is out of valid range", data.timestamp),
                severity: ErrorSeverity::High,
            });
            quality_score -= 0.2;
        }

        // 验证车牌号格式
        if let Some(ref plate_no) = data.plate_no {
            if !self.plate_regex.is_match(plate_no) {
                errors.push(ValidationError {
                    field: "plate_no".to_string(),
                    error_type: ValidationErrorType::InvalidFormat,
                    message: format!("Invalid plate number format: {}", plate_no),
                    severity: ErrorSeverity::Medium,
                });
                quality_score -= 0.1;
            }
        }

        // 验证费用范围
        if let Some(fee) = data.recievable_fee {
            if fee < self.fee_range.0 || fee > self.fee_range.1 {
                warnings.push(ValidationWarning {
                    field: "recievable_fee".to_string(),
                    message: format!("Fee {} is outside normal range", fee),
                    suggestion: Some("Please verify the fee amount".to_string()),
                });
                quality_score -= 0.05;
            }
        }

        Ok(ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
            quality_score: quality_score.max(0.0),
        })
    }
}

/// 业务规则验证器
pub struct BusinessValidator {
    vehicle_type_rules: HashMap<String, VehicleTypeRule>,
    fee_calculation_rules: FeeCalculationRules,
}

#[derive(Debug, Clone)]
pub struct VehicleTypeRule {
    pub valid_fee_types: Vec<String>,
    pub min_fee: u32,
    pub max_fee: u32,
}

#[derive(Debug, Clone)]
pub struct FeeCalculationRules {
    pub fee_tolerance: f32, // 允许的费用差异百分比
    pub require_both_fees: bool, // 是否要求应收和实收都存在
}

impl BusinessValidator {
    pub fn new(config: BusinessValidationConfig) -> Self {
        Self {
            vehicle_type_rules: config.vehicle_type_rules,
            fee_calculation_rules: config.fee_calculation_rules,
        }
    }

    pub async fn validate(&self, data: &NormalizedData) -> Result<ValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut quality_score = 1.0f32;

        // 验证车辆类型和收费类型的匹配
        if let (Some(ref vehicle_type), Some(ref fee_type)) = (&data.vehicle_type, &data.vehicle_fee_type) {
            if let Some(rule) = self.vehicle_type_rules.get(vehicle_type) {
                if !rule.valid_fee_types.contains(fee_type) {
                    errors.push(ValidationError {
                        field: "vehicle_fee_type".to_string(),
                        error_type: ValidationErrorType::BusinessRuleViolation,
                        message: format!("Fee type {} is not valid for vehicle type {}", fee_type, vehicle_type),
                        severity: ErrorSeverity::High,
                    });
                    quality_score -= 0.2;
                }
            }
        }

        // 验证费用一致性
        if let (Some(receivable), Some(actual)) = (data.recievable_fee, data.actual_fee) {
            let difference_ratio = (receivable as f32 - actual as f32).abs() / receivable as f32;
            if difference_ratio > self.fee_calculation_rules.fee_tolerance {
                warnings.push(ValidationWarning {
                    field: "fee_consistency".to_string(),
                    message: format!("Large difference between receivable ({}) and actual ({}) fees", receivable, actual),
                    suggestion: Some("Please verify fee calculation".to_string()),
                });
                quality_score -= 0.1;
            }
        }

        // 验证交易数据完整性
        if data.is_transaction() {
            if data.macid.is_none() {
                errors.push(ValidationError {
                    field: "macid".to_string(),
                    error_type: ValidationErrorType::MissingRequired,
                    message: "MAC ID is required for transaction data".to_string(),
                    severity: ErrorSeverity::High,
                });
                quality_score -= 0.2;
            }

            if self.fee_calculation_rules.require_both_fees {
                if data.recievable_fee.is_none() || data.actual_fee.is_none() {
                    errors.push(ValidationError {
                        field: "fees".to_string(),
                        error_type: ValidationErrorType::MissingRequired,
                        message: "Both receivable and actual fees are required".to_string(),
                        severity: ErrorSeverity::Medium,
                    });
                    quality_score -= 0.15;
                }
            }
        }

        // 验证摄像头数据完整性
        if data.is_vehicle_data() {
            if data.camera_code.is_none() {
                errors.push(ValidationError {
                    field: "camera_code".to_string(),
                    error_type: ValidationErrorType::MissingRequired,
                    message: "Camera code is required for vehicle data".to_string(),
                    severity: ErrorSeverity::High,
                });
                quality_score -= 0.2;
            }
        }

        Ok(ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
            quality_score: quality_score.max(0.0),
        })
    }
}

/// 质量验证器
pub struct QualityValidator {
    completeness_weights: HashMap<String, f32>,
    accuracy_thresholds: HashMap<String, f32>,
}

impl QualityValidator {
    pub fn new(config: QualityValidationConfig) -> Self {
        Self {
            completeness_weights: config.completeness_weights,
            accuracy_thresholds: config.accuracy_thresholds,
        }
    }

    pub async fn validate(&self, data: &NormalizedData) -> Result<ValidationResult> {
        let mut warnings = Vec::new();
        let mut quality_score = 1.0f32;

        // 计算数据完整性得分
        let completeness_score = self.calculate_completeness_score(data);
        quality_score *= completeness_score;

        if completeness_score < 0.8 {
            warnings.push(ValidationWarning {
                field: "completeness".to_string(),
                message: format!("Data completeness score is low: {:.2}", completeness_score),
                suggestion: Some("Consider enriching the data with missing fields".to_string()),
            });
        }

        // 检查数据一致性
        let consistency_score = self.calculate_consistency_score(data);
        quality_score *= consistency_score;

        if consistency_score < 0.9 {
            warnings.push(ValidationWarning {
                field: "consistency".to_string(),
                message: format!("Data consistency score is low: {:.2}", consistency_score),
                suggestion: Some("Check for data inconsistencies".to_string()),
            });
        }

        Ok(ValidationResult {
            is_valid: true, // 质量验证不产生错误，只产生警告
            errors: Vec::new(),
            warnings,
            quality_score: quality_score.max(0.0),
        })
    }

    fn calculate_completeness_score(&self, data: &NormalizedData) -> f32 {
        let mut total_weight = 0.0f32;
        let mut present_weight = 0.0f32;

        // 检查各字段的完整性
        let fields = vec![
            ("plate_no", data.plate_no.is_some()),
            ("vehicle_type", data.vehicle_type.is_some()),
            ("camera_code", data.camera_code.is_some()),
            ("macid", data.macid.is_some()),
            ("recievable_fee", data.recievable_fee.is_some()),
            ("actual_fee", data.actual_fee.is_some()),
        ];

        for (field_name, is_present) in fields {
            let weight = self.completeness_weights.get(field_name).copied().unwrap_or(1.0);
            total_weight += weight;
            if is_present {
                present_weight += weight;
            }
        }

        if total_weight > 0.0 {
            present_weight / total_weight
        } else {
            1.0
        }
    }

    fn calculate_consistency_score(&self, data: &NormalizedData) -> f32 {
        let mut consistency_score = 1.0f32;

        // 检查交易数据和摄像头数据的一致性
        if data.is_transaction() && data.is_vehicle_data() {
            // 如果同时包含交易和摄像头数据，检查车牌号是否一致
            // 这种情况在数据融合时可能出现
            consistency_score = 1.0; // 暂时不做特殊处理
        }

        // 检查费用的合理性
        if let (Some(receivable), Some(actual)) = (data.recievable_fee, data.actual_fee) {
            if receivable > 0 && actual > receivable * 2 {
                consistency_score *= 0.8; // 实收费用过高
            }
        }

        consistency_score
    }
}
```

## 6. 数据清洗器

### 6.1 统一清洗器实现

```rust
// 数据清洗器
use anyhow::Result;
use types::NormalizedData;
use std::collections::HashSet;

pub struct DataCleaner {
    missing_value_handler: MissingValueHandler,
    outlier_detector: OutlierDetector,
    duplicate_remover: DuplicateRemover,
    cleaning_config: CleaningConfig,
}

#[derive(Debug, Clone)]
pub struct CleaningConfig {
    pub handle_missing_values: bool,
    pub detect_outliers: bool,
    pub remove_duplicates: bool,
    pub duplicate_time_window: u64, // 毫秒
}

#[derive(Debug, Clone)]
pub enum CleaningAction {
    MissingValueFilled { field: String, old_value: Option<String>, new_value: String },
    OutlierDetected { field: String, value: String, reason: String },
    DuplicateRemoved { duplicate_id: String, original_id: String },
    DataNormalized { field: String, old_value: String, new_value: String },
}

impl DataCleaner {
    pub fn new(config: CleaningConfig) -> Self {
        Self {
            missing_value_handler: MissingValueHandler::new(),
            outlier_detector: OutlierDetector::new(),
            duplicate_remover: DuplicateRemover::new(config.duplicate_time_window),
            cleaning_config: config,
        }
    }

    /// 清洗数据
    pub async fn clean(&mut self, data: &mut NormalizedData) -> Result<Vec<CleaningAction>> {
        let mut actions = Vec::new();

        // 处理缺失值
        if self.cleaning_config.handle_missing_values {
            let missing_actions = self.missing_value_handler.handle(data).await?;
            actions.extend(missing_actions);
        }

        // 检测异常值
        if self.cleaning_config.detect_outliers {
            let outlier_actions = self.outlier_detector.detect(data).await?;
            actions.extend(outlier_actions);
        }

        // 数据标准化
        let normalization_actions = self.normalize_data(data).await?;
        actions.extend(normalization_actions);

        Ok(actions)
    }

    /// 检查重复数据
    pub async fn check_duplicate(&mut self, data: &NormalizedData) -> Result<Option<CleaningAction>> {
        if self.cleaning_config.remove_duplicates {
            self.duplicate_remover.check_duplicate(data).await
        } else {
            Ok(None)
        }
    }

    /// 数据标准化
    async fn normalize_data(&self, data: &mut NormalizedData) -> Result<Vec<CleaningAction>> {
        let mut actions = Vec::new();

        // 标准化车牌号
        if let Some(ref mut plate_no) = data.plate_no {
            let old_value = plate_no.clone();
            let normalized = plate_no.trim().to_uppercase().replace("-", "");
            if normalized != old_value {
                *plate_no = normalized.clone();
                actions.push(CleaningAction::DataNormalized {
                    field: "plate_no".to_string(),
                    old_value,
                    new_value: normalized,
                });
            }
        }

        Ok(actions)
    }
}
```

## 7. 格式转换器

### 7.1 统一格式转换器

```rust
// 格式转换器
use anyhow::{Result, anyhow};
use serde_json;
use types::NormalizedData;

pub struct FormatConverter {
    json_formatter: JsonFormatter,
    binary_formatter: BinaryFormatter,
    custom_formatters: std::collections::HashMap<String, Box<dyn CustomFormatter>>,
}

pub trait CustomFormatter: Send + Sync {
    fn format(&self, data: &NormalizedData) -> Result<Vec<u8>>;
    fn get_format_name(&self) -> &str;
    fn get_content_type(&self) -> &str;
}

impl FormatConverter {
    pub fn new() -> Self {
        Self {
            json_formatter: JsonFormatter::new(),
            binary_formatter: BinaryFormatter::new(),
            custom_formatters: std::collections::HashMap::new(),
        }
    }

    /// 注册自定义格式器
    pub fn register_formatter(&mut self, formatter: Box<dyn CustomFormatter>) {
        let name = formatter.get_format_name().to_string();
        self.custom_formatters.insert(name, formatter);
    }

    /// 转换为指定格式
    pub async fn convert(&self, data: &NormalizedData, format: &OutputFormatType) -> Result<OutputFormat> {
        match format {
            OutputFormatType::Json => {
                let json_str = self.json_formatter.format(data).await?;
                Ok(OutputFormat::Json(json_str))
            }
            OutputFormatType::Binary => {
                let binary_data = self.binary_formatter.format(data).await?;
                Ok(OutputFormat::Binary(binary_data))
            }
            OutputFormatType::Custom(format_name) => {
                if let Some(formatter) = self.custom_formatters.get(format_name) {
                    let custom_data = formatter.format(data)?;
                    Ok(OutputFormat::Custom(format_name.clone(), custom_data))
                } else {
                    Err(anyhow!("Unknown custom format: {}", format_name))
                }
            }
        }
    }
}

#[derive(Debug, Clone)]
pub enum OutputFormatType {
    Json,
    Binary,
    Custom(String),
}

/// JSON格式器
pub struct JsonFormatter {
    pretty_print: bool,
    include_metadata: bool,
}

impl JsonFormatter {
    pub fn new() -> Self {
        Self {
            pretty_print: false,
            include_metadata: true,
        }
    }

    pub async fn format(&self, data: &NormalizedData) -> Result<String> {
        let mut json_data = serde_json::to_value(data)?;

        // 添加元数据
        if self.include_metadata {
            if let serde_json::Value::Object(ref mut map) = json_data {
                map.insert("_metadata".to_string(), serde_json::json!({
                    "format_version": "1.0",
                    "formatted_at": chrono::Utc::now().timestamp_millis(),
                    "data_type": if data.is_transaction() { "transaction" } else { "vehicle_data" }
                }));
            }
        }

        if self.pretty_print {
            Ok(serde_json::to_string_pretty(&json_data)?)
        } else {
            Ok(serde_json::to_string(&json_data)?)
        }
    }
}

/// 二进制格式器
pub struct BinaryFormatter {
    compression_enabled: bool,
    version: u8,
}

impl BinaryFormatter {
    pub fn new() -> Self {
        Self {
            compression_enabled: true,
            version: 1,
        }
    }

    pub async fn format(&self, data: &NormalizedData) -> Result<Vec<u8>> {
        // 序列化为JSON字节
        let json_bytes = serde_json::to_vec(data)?;

        // 构建二进制格式
        let mut binary_data = Vec::new();

        // 添加头部信息
        binary_data.extend_from_slice(b"HCS\0"); // 魔数
        binary_data.push(self.version); // 版本号

        // 添加标志位
        let mut flags = 0u8;
        if self.compression_enabled {
            flags |= 0x01;
        }
        if data.is_transaction() {
            flags |= 0x02;
        }
        if data.is_vehicle_data() {
            flags |= 0x04;
        }
        binary_data.push(flags);

        // 添加时间戳
        binary_data.extend_from_slice(&data.timestamp.to_be_bytes());

        // 处理数据内容
        let content = if self.compression_enabled {
            self.compress_data(&json_bytes).await?
        } else {
            json_bytes
        };

        // 添加内容长度
        binary_data.extend_from_slice(&(content.len() as u32).to_be_bytes());

        // 添加内容
        binary_data.extend_from_slice(&content);

        // 添加校验和
        let checksum = self.calculate_checksum(&binary_data);
        binary_data.extend_from_slice(&checksum.to_be_bytes());

        Ok(binary_data)
    }

    async fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        // 使用简单的压缩算法（实际应用中可以使用更高效的压缩算法）
        use flate2::write::GzEncoder;
        use flate2::Compression;
        use std::io::Write;

        let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(data)?;
        Ok(encoder.finish()?)
    }

    fn calculate_checksum(&self, data: &[u8]) -> u32 {
        // 简单的CRC32校验
        crc32fast::hash(data)
    }
}

/// CSV格式器（自定义格式器示例）
pub struct CsvFormatter {
    include_header: bool,
    delimiter: char,
}

impl CsvFormatter {
    pub fn new() -> Self {
        Self {
            include_header: true,
            delimiter: ',',
        }
    }
}

impl CustomFormatter for CsvFormatter {
    fn format(&self, data: &NormalizedData) -> Result<Vec<u8>> {
        let mut csv_content = String::new();

        // 添加头部
        if self.include_header {
            csv_content.push_str("id,gantry_code,timestamp,plate_no,vehicle_type,camera_code,macid,recievable_fee,actual_fee\n");
        }

        // 添加数据行
        csv_content.push_str(&format!(
            "{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}{}\n",
            data.id,
            self.delimiter,
            data.gantry_code,
            self.delimiter,
            data.timestamp,
            self.delimiter,
            data.plate_no.as_deref().unwrap_or(""),
            self.delimiter,
            data.vehicle_type.as_deref().unwrap_or(""),
            self.delimiter,
            data.camera_code.as_deref().unwrap_or(""),
            self.delimiter,
            data.macid.as_deref().unwrap_or(""),
            self.delimiter,
            data.recievable_fee.map(|f| f.to_string()).unwrap_or_default(),
            self.delimiter,
            data.actual_fee.map(|f| f.to_string()).unwrap_or_default()
        ));

        Ok(csv_content.into_bytes())
    }

    fn get_format_name(&self) -> &str {
        "csv"
    }

    fn get_content_type(&self) -> &str {
        "text/csv"
    }
}
```

## 8. 配置管理

### 8.1 处理器配置

```yaml
# config/data_processor.yaml
data_processor:
  # 归一化配置
  normalizer:
    default_values:
      default_vehicle_type: "小型车"
      default_plate_color: "蓝色"
      quality_threshold: 0.7

    field_mappings:
      vehicle_type_mapping:
        "1": "小型车"
        "2": "中型车"
        "3": "大型车"
        "4": "特大型车"

      plate_type_mapping:
        "1": "蓝牌"
        "2": "黄牌"
        "3": "白牌"
        "4": "黑牌"
        "5": "绿牌"

      fee_type_mapping:
        "1": "客车"
        "2": "货车"
        "3": "专项作业车"

  # 验证配置
  validator:
    enabled_rules:
      schema_validation: true
      business_validation: true
      quality_validation: true

    schema_rules:
      plate_pattern: "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$"
      timestamp_range: [1640995200000, 4102444800000]  # 2022-2100
      fee_range: [0, 100000]  # 0-1000元（分）

    business_rules:
      vehicle_type_rules:
        "小型车":
          valid_fee_types: ["客车"]
          min_fee: 0
          max_fee: 5000
        "大型车":
          valid_fee_types: ["客车", "货车"]
          min_fee: 0
          max_fee: 20000

      fee_calculation_rules:
        fee_tolerance: 0.1  # 10%
        require_both_fees: true

    quality_rules:
      completeness_weights:
        plate_no: 2.0
        vehicle_type: 1.5
        camera_code: 1.0
        macid: 1.0
        recievable_fee: 1.5
        actual_fee: 1.5

      accuracy_thresholds:
        plate_confidence: 0.8
        vehicle_confidence: 0.7

  # 清洗配置
  cleaner:
    handle_missing_values: true
    detect_outliers: true
    remove_duplicates: true
    duplicate_time_window: 5000  # 5秒内的重复数据

  # 格式转换配置
  formatter:
    default_format: "json"
    json:
      pretty_print: false
      include_metadata: true
    binary:
      compression_enabled: true
      version: 1
    custom_formats:
      - name: "csv"
        enabled: true
        include_header: true
        delimiter: ","

  # 性能配置
  performance:
    batch_size: 100
    max_concurrent_tasks: 10
    processing_timeout: 30000  # 30秒
    memory_limit: "1GB"
```

## 9. 性能优化

### 9.1 批量处理优化

```rust
// 批量处理器
use anyhow::Result;
use tokio::sync::Semaphore;
use std::sync::Arc;

pub struct BatchProcessor {
    data_processor: Arc<DataProcessor>,
    batch_size: usize,
    max_concurrent_batches: usize,
    semaphore: Arc<Semaphore>,
}

impl BatchProcessor {
    pub fn new(processor: DataProcessor, config: BatchProcessorConfig) -> Self {
        Self {
            data_processor: Arc::new(processor),
            batch_size: config.batch_size,
            max_concurrent_batches: config.max_concurrent_batches,
            semaphore: Arc::new(Semaphore::new(config.max_concurrent_batches)),
        }
    }

    /// 批量处理数据
    pub async fn process_batch(&self, data: Vec<NormalizedData>) -> Result<Vec<ProcessedData>> {
        let chunks: Vec<_> = data.chunks(self.batch_size).collect();
        let mut handles = Vec::new();

        for chunk in chunks {
            let chunk_data = chunk.to_vec();
            let processor = Arc::clone(&self.data_processor);
            let semaphore = Arc::clone(&self.semaphore);

            let handle = tokio::spawn(async move {
                let _permit = semaphore.acquire().await.unwrap();
                processor.process_batch(chunk_data).await
            });

            handles.push(handle);
        }

        let mut results = Vec::new();
        for handle in handles {
            let batch_result = handle.await??;
            results.extend(batch_result);
        }

        Ok(results)
    }
}
```

## 10. 监控和指标

### 10.1 处理指标收集

```rust
// 处理指标收集器
use std::sync::atomic::{AtomicU64, AtomicF64, Ordering};
use std::time::Instant;

pub struct ProcessingMetrics {
    total_processed: AtomicU64,
    success_count: AtomicU64,
    error_count: AtomicU64,
    total_processing_time: AtomicU64,
    validation_errors: AtomicU64,
    cleaning_actions: AtomicU64,
}

impl ProcessingMetrics {
    pub fn new() -> Self {
        Self {
            total_processed: AtomicU64::new(0),
            success_count: AtomicU64::new(0),
            error_count: AtomicU64::new(0),
            total_processing_time: AtomicU64::new(0),
            validation_errors: AtomicU64::new(0),
            cleaning_actions: AtomicU64::new(0),
        }
    }

    pub fn record_processing(&self, start_time: Instant, success: bool) {
        let processing_time = start_time.elapsed().as_millis() as u64;

        self.total_processed.fetch_add(1, Ordering::Relaxed);
        self.total_processing_time.fetch_add(processing_time, Ordering::Relaxed);

        if success {
            self.success_count.fetch_add(1, Ordering::Relaxed);
        } else {
            self.error_count.fetch_add(1, Ordering::Relaxed);
        }
    }

    pub fn record_validation_error(&self) {
        self.validation_errors.fetch_add(1, Ordering::Relaxed);
    }

    pub fn record_cleaning_action(&self) {
        self.cleaning_actions.fetch_add(1, Ordering::Relaxed);
    }

    pub fn get_statistics(&self) -> ProcessorStatistics {
        let total = self.total_processed.load(Ordering::Relaxed);
        let success = self.success_count.load(Ordering::Relaxed);
        let errors = self.error_count.load(Ordering::Relaxed);
        let total_time = self.total_processing_time.load(Ordering::Relaxed);

        ProcessorStatistics {
            total_processed: total,
            success_count: success,
            error_count: errors,
            average_processing_time_ms: if total > 0 { total_time as f64 / total as f64 } else { 0.0 },
            throughput_per_second: if total_time > 0 { (total as f64 * 1000.0) / total_time as f64 } else { 0.0 },
        }
    }
}
```

## 11. 总结

数据处理模块是数据采集系统的核心处理引擎，负责确保数据质量和格式统一。本设计文档详细描述了：

1. **分层处理架构**: 归一化、验证、清洗、增强、转换的完整处理流程
2. **灵活的验证体系**: 模式验证、业务规则验证、质量验证的多层验证机制
3. **智能数据清洗**: 缺失值处理、异常值检测、重复数据移除等清洗功能
4. **多格式支持**: JSON、二进制、自定义格式的灵活转换能力
5. **性能优化**: 批量处理、并发控制、内存管理等性能优化策略
6. **全面监控**: 处理指标收集、性能监控、质量评估等监控能力

该设计确保了数据处理的高效性、准确性和可靠性，为后续的数据传输和存储提供了高质量的数据基础。
