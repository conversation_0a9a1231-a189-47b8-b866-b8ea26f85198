# DDD-002 数据采集模块详细设计文档

**文档编号：** DDD-002  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 数据采集团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md  
**上级设计：** DDD-001-系统总体架构详细设计文档.md

---

## 1. 模块概述

### 1.1 模块目的
数据采集模块负责从多种数据源实时采集交易日志和摄像头抓拍数据，是整个数据采集系统的核心入口模块。

### 1.2 需求追溯
本模块对应PRD-001中的以下需求：
- **FR-001**: 第三方应用日志采集
  - 被动接收：外部应用主动同步日志到本机指定目录
  - 主动获取：通过SFTP协议定时下载远程日志文件
- **FR-002**: 摄像头数据采集
  - 通过摄像头厂商SDK实时采集抓拍数据
  - 支持多路摄像头并发采集

### 1.3 模块职责
- **Transaction Crate**: 负责交易日志数据的采集和预处理
- **Image Crate**: 负责摄像头数据的采集和管理
- **Types Crate**: 提供数据类型定义和通用接口

## 2. 模块架构设计

### 2.1 模块架构图

```mermaid
graph TB
    subgraph "数据源"
        DS1[本地日志文件]
        DS2[SFTP远程服务器]
        DS3[摄像头SDK]
    end

    subgraph "Transaction Crate"
        subgraph "采集器实现Transaction"
            TC1[PassiveLogCollector<br/>被动日志采集器]
            TC2[SftpLogCollector<br/>SFTP日志采集器]
        end
        subgraph "支持组件Transaction"
            TC3[FileWatcher<br/>文件监控器]
            TC4[SftpClient<br/>SFTP客户端]
            TC5[LogParser<br/>日志解析器]
        end
    end

    subgraph "Image Crate"
        subgraph "采集器实现Image"
            IC1[CameraCollector<br/>摄像头采集器]
        end
        subgraph "支持组件Camera"
            IC2[SdkManager<br/>SDK管理器]
            IC3[DeviceManager<br/>设备管理器]
            IC4[StreamProcessor<br/>流处理器]
        end
    end

    subgraph "Types Crate"
        TY1[Transaction<br/>交易数据结构]
        TY2[VehicleData<br/>摄像头数据结构]
        TY3[NormalizedData<br/>归一化数据结构]
        TY4[Collector Trait<br/>采集器接口]
        TY5[Error Types<br/>错误类型定义]
    end

    DS1 --> TC1
    DS2 --> TC2
    DS3 --> IC1

    TC1 --> TC3
    TC2 --> TC4
    TC1 --> TC5
    TC2 --> TC5

    IC1 --> IC2
    IC1 --> IC3
    IC1 --> IC4

    TC1 -.-> TY4
    TC2 -.-> TY4
    IC1 -.-> TY4

    TC5 --> TY1
    IC4 --> TY2
    TY1 --> TY3
    TY2 --> TY3
```

### 2.2 接口设计

#### 2.2.1 核心接口定义

```rust
// types/src/lib.rs
use anyhow::Result;
use serde::{Deserialize, Serialize};

/// 统一的数据采集器接口
pub trait Collector: Send + Sync {
    /// 执行数据采集，返回归一化数据
    fn collect(&mut self) -> Result<Option<Vec<NormalizedData>>>;
    
    /// 获取采集器状态
    fn get_status(&self) -> CollectorStatus;
    
    /// 启动采集器
    fn start(&mut self) -> Result<()>;
    
    /// 停止采集器
    fn stop(&mut self) -> Result<()>;
}

/// 采集器状态枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CollectorStatus {
    Stopped,
    Starting,
    Running,
    Error(String),
}
```

#### 2.2.2 数据结构定义

```rust
// types/src/data.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 交易日志信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Transaction {
    pub timestamp: u64,
    pub macid: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_fee_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_actual_fee: Option<u32>,
}

/// 摄像头抓拍车辆信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VehicleData {
    pub timestamp: u64,
    pub camera_code: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_color: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_cat: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_size: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_object: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub image_bytes: Option<Vec<u8>>,
}

/// 归一化数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NormalizedData {
    pub id: String,
    pub gantry_code: String,
    pub timestamp: u64,
    
    // 车辆基础信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_no: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_type: Option<String>,
    
    // 抓拍相关信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub camera_code: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub plate_color: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_cat: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_size: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_object: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub image_bytes: Option<Vec<u8>>,
    
    // 收费相关信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub macid: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub vehicle_fee_type: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub actual_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_recievable_fee: Option<u32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pro_actual_fee: Option<u32>,
}

impl NormalizedData {
    /// 从交易数据创建归一化数据
    pub fn from_transaction(gantry_code: String, transaction: Transaction) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: transaction.timestamp,
            plate_no: transaction.plate_no,
            vehicle_type: transaction.vehicle_type,
            macid: Some(transaction.macid),
            vehicle_fee_type: transaction.vehicle_fee_type,
            recievable_fee: transaction.recievable_fee,
            actual_fee: transaction.actual_fee,
            pro_recievable_fee: transaction.pro_recievable_fee,
            pro_actual_fee: transaction.pro_actual_fee,
            // 摄像头相关字段为空
            camera_code: None,
            plate_type: None,
            plate_color: None,
            vehicle_cat: None,
            vehicle_size: None,
            vehicle_object: None,
            image_bytes: None,
        }
    }
    
    /// 从摄像头数据创建归一化数据
    pub fn from_vehicle_data(gantry_code: String, vehicle_data: VehicleData) -> Self {
        Self {
            id: Uuid::new_v4().to_string(),
            gantry_code,
            timestamp: vehicle_data.timestamp,
            plate_no: vehicle_data.plate_no,
            vehicle_type: vehicle_data.vehicle_type,
            camera_code: Some(vehicle_data.camera_code),
            plate_type: vehicle_data.plate_type,
            plate_color: vehicle_data.plate_color,
            vehicle_cat: vehicle_data.vehicle_cat,
            vehicle_size: vehicle_data.vehicle_size,
            vehicle_object: vehicle_data.vehicle_object,
            image_bytes: vehicle_data.image_bytes,
            // 收费相关字段为空
            macid: None,
            vehicle_fee_type: None,
            recievable_fee: None,
            actual_fee: None,
            pro_recievable_fee: None,
            pro_actual_fee: None,
        }
    }
    
    /// 判断是否为交易数据
    pub fn is_transaction(&self) -> bool {
        self.macid.is_some()
    }
    
    /// 判断是否为摄像头数据
    pub fn is_vehicle_data(&self) -> bool {
        self.camera_code.is_some()
    }
}
```

## 3. Transaction Crate详细设计

### 3.1 被动日志采集器设计

#### 3.1.1 功能描述
被动日志采集器监控指定目录下的日志文件变化，实时读取新增的日志内容并解析为交易数据。

#### 3.1.2 实现架构

```mermaid
sequenceDiagram
    participant FS as 文件系统
    participant FW as FileWatcher
    participant PLC as PassiveLogCollector
    participant LP as LogParser
    participant ND as NormalizedData

    FS->>FW: 文件变更事件
    FW->>PLC: 通知文件变更
    PLC->>FS: 读取新增内容
    FS->>PLC: 返回日志内容
    PLC->>LP: 解析日志行
    LP->>PLC: 返回Transaction
    PLC->>ND: 转换为NormalizedData
    ND->>PLC: 返回归一化数据
```

#### 3.1.3 核心实现

```rust
// transaction/src/passive.rs
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::Mutex;
use notify::{Watcher, RecursiveMode, Event, EventKind};
use anyhow::{Result, Context};
use types::{Collector, CollectorStatus, NormalizedData, Transaction};

pub struct PassiveLogCollector {
    watch_directory: PathBuf,
    file_pattern: String,
    gantry_code: String,
    status: CollectorStatus,
    file_positions: Arc<Mutex<HashMap<PathBuf, u64>>>,
    log_parser: LogParser,
    watcher: Option<notify::RecommendedWatcher>,
}

impl PassiveLogCollector {
    pub fn new(
        watch_directory: impl AsRef<Path>,
        file_pattern: String,
        gantry_code: String,
    ) -> Self {
        Self {
            watch_directory: watch_directory.as_ref().to_path_buf(),
            file_pattern,
            gantry_code,
            status: CollectorStatus::Stopped,
            file_positions: Arc::new(Mutex::new(HashMap::new())),
            log_parser: LogParser::new(),
            watcher: None,
        }
    }

    /// 处理文件变更事件
    async fn handle_file_event(&mut self, event: Event) -> Result<Vec<NormalizedData>> {
        let mut results = Vec::new();

        if let EventKind::Modify(_) = event.kind {
            for path in event.paths {
                if self.matches_pattern(&path) {
                    let new_data = self.read_file_incremental(&path).await?;
                    results.extend(new_data);
                }
            }
        }

        Ok(results)
    }

    /// 增量读取文件内容
    async fn read_file_incremental(&mut self, path: &Path) -> Result<Vec<NormalizedData>> {
        let mut positions = self.file_positions.lock().await;
        let current_pos = positions.get(path).copied().unwrap_or(0);

        let file_content = tokio::fs::read_to_string(path).await
            .context("Failed to read file")?;

        let new_content = &file_content[current_pos as usize..];
        let new_pos = file_content.len() as u64;
        positions.insert(path.to_path_buf(), new_pos);

        let mut results = Vec::new();
        for line in new_content.lines() {
            if let Ok(transaction) = self.log_parser.parse_line(line) {
                let normalized = NormalizedData::from_transaction(
                    self.gantry_code.clone(),
                    transaction
                );
                results.push(normalized);
            }
        }

        Ok(results)
    }

    /// 检查文件是否匹配模式
    fn matches_pattern(&self, path: &Path) -> bool {
        if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
            glob::Pattern::new(&self.file_pattern)
                .map(|pattern| pattern.matches(filename))
                .unwrap_or(false)
        } else {
            false
        }
    }
}

impl Collector for PassiveLogCollector {
    fn collect(&mut self) -> Result<Option<Vec<NormalizedData>>> {
        // 被动采集器通过事件驱动，这里返回None
        // 实际数据通过事件处理器异步收集
        Ok(None)
    }

    fn get_status(&self) -> CollectorStatus {
        self.status.clone()
    }

    fn start(&mut self) -> Result<()> {
        self.status = CollectorStatus::Starting;

        // 初始化文件监控器
        let (tx, rx) = std::sync::mpsc::channel();
        let mut watcher = notify::recommended_watcher(tx)?;

        watcher.watch(&self.watch_directory, RecursiveMode::NonRecursive)?;
        self.watcher = Some(watcher);

        self.status = CollectorStatus::Running;
        Ok(())
    }

    fn stop(&mut self) -> Result<()> {
        self.watcher = None;
        self.status = CollectorStatus::Stopped;
        Ok(())
    }
}
```

### 3.2 SFTP主动采集器设计

#### 3.2.1 功能描述
SFTP主动采集器定时连接远程SFTP服务器，下载新的日志文件并进行增量同步。

#### 3.2.2 实现架构

```mermaid
sequenceDiagram
    participant Scheduler as 调度器
    participant SLC as SftpLogCollector
    participant SFTP as SFTP客户端
    participant Remote as 远程服务器
    participant LP as LogParser

    Scheduler->>SLC: 触发采集任务
    SLC->>SFTP: 建立连接
    SFTP->>Remote: 连接认证
    Remote->>SFTP: 认证成功
    SLC->>SFTP: 列出文件列表
    SFTP->>Remote: LIST命令
    Remote->>SFTP: 返回文件列表
    SLC->>SLC: 比较文件时间戳
    SLC->>SFTP: 下载新文件
    SFTP->>Remote: GET命令
    Remote->>SFTP: 传输文件内容
    SLC->>LP: 解析文件内容
    LP->>SLC: 返回Transaction列表
```

#### 3.2.3 核心实现

```rust
// transaction/src/sftp.rs
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::time::{interval, Duration};
use ssh2::Session;
use anyhow::{Result, Context};
use types::{Collector, CollectorStatus, NormalizedData, Transaction};

pub struct SftpLogCollector {
    host: String,
    port: u16,
    username: String,
    password: String,
    remote_directory: String,
    file_pattern: String,
    gantry_code: String,
    status: CollectorStatus,
    last_sync_time: HashMap<String, u64>,
    log_parser: LogParser,
    sync_interval: Duration,
}

impl SftpLogCollector {
    pub fn new(
        host: String,
        port: u16,
        username: String,
        password: String,
        remote_directory: String,
        file_pattern: String,
        gantry_code: String,
        sync_interval_secs: u64,
    ) -> Self {
        Self {
            host,
            port,
            username,
            password,
            remote_directory,
            file_pattern,
            gantry_code,
            status: CollectorStatus::Stopped,
            last_sync_time: HashMap::new(),
            log_parser: LogParser::new(),
            sync_interval: Duration::from_secs(sync_interval_secs),
        }
    }

    /// 建立SFTP连接
    async fn connect_sftp(&self) -> Result<ssh2::Sftp> {
        let tcp = std::net::TcpStream::connect(format!("{}:{}", self.host, self.port))
            .context("Failed to connect to SFTP server")?;

        let mut session = Session::new()?;
        session.set_tcp_stream(tcp);
        session.handshake()?;
        session.userauth_password(&self.username, &self.password)?;

        let sftp = session.sftp()?;
        Ok(sftp)
    }

    /// 列出远程文件
    async fn list_remote_files(&self, sftp: &ssh2::Sftp) -> Result<Vec<(String, u64)>> {
        let mut files = Vec::new();
        let dir_path = Path::new(&self.remote_directory);

        for entry in sftp.readdir(dir_path)? {
            let (path, stat) = entry;
            if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                if self.matches_pattern(filename) {
                    let mtime = stat.mtime.unwrap_or(0) as u64;
                    files.push((filename.to_string(), mtime));
                }
            }
        }

        Ok(files)
    }

    /// 下载并处理文件
    async fn download_and_process_file(
        &mut self,
        sftp: &ssh2::Sftp,
        filename: &str,
    ) -> Result<Vec<NormalizedData>> {
        let remote_path = Path::new(&self.remote_directory).join(filename);
        let mut file = sftp.open(&remote_path)?;

        let mut content = String::new();
        std::io::Read::read_to_string(&mut file, &mut content)?;

        let mut results = Vec::new();
        for line in content.lines() {
            if let Ok(transaction) = self.log_parser.parse_line(line) {
                let normalized = NormalizedData::from_transaction(
                    self.gantry_code.clone(),
                    transaction
                );
                results.push(normalized);
            }
        }

        Ok(results)
    }

    /// 检查文件是否匹配模式
    fn matches_pattern(&self, filename: &str) -> bool {
        glob::Pattern::new(&self.file_pattern)
            .map(|pattern| pattern.matches(filename))
            .unwrap_or(false)
    }

    /// 执行同步任务
    async fn sync_files(&mut self) -> Result<Vec<NormalizedData>> {
        let sftp = self.connect_sftp().await?;
        let remote_files = self.list_remote_files(&sftp).await?;

        let mut all_results = Vec::new();

        for (filename, mtime) in remote_files {
            let last_sync = self.last_sync_time.get(&filename).copied().unwrap_or(0);

            if mtime > last_sync {
                let results = self.download_and_process_file(&sftp, &filename).await?;
                all_results.extend(results);
                self.last_sync_time.insert(filename, mtime);
            }
        }

        Ok(all_results)
    }
}

impl Collector for SftpLogCollector {
    fn collect(&mut self) -> Result<Option<Vec<NormalizedData>>> {
        // SFTP采集器需要异步执行，这里返回None
        // 实际采集通过定时任务执行
        Ok(None)
    }

    fn get_status(&self) -> CollectorStatus {
        self.status.clone()
    }

    fn start(&mut self) -> Result<()> {
        self.status = CollectorStatus::Starting;

        // 启动定时同步任务
        let mut interval = interval(self.sync_interval);

        tokio::spawn(async move {
            loop {
                interval.tick().await;
                // 执行同步逻辑
            }
        });

        self.status = CollectorStatus::Running;
        Ok(())
    }

    fn stop(&mut self) -> Result<()> {
        self.status = CollectorStatus::Stopped;
        Ok(())
    }
}
```

### 3.3 日志解析器设计

#### 3.3.1 功能描述
日志解析器负责将不同格式的日志行解析为标准的Transaction结构。

#### 3.3.2 支持的日志格式

```rust
// transaction/src/parser.rs
use anyhow::{Result, anyhow};
use serde_json;
use chrono::{DateTime, Utc};
use types::Transaction;

pub struct LogParser {
    format_detectors: Vec<Box<dyn FormatDetector>>,
}

pub trait FormatDetector: Send + Sync {
    fn detect(&self, line: &str) -> bool;
    fn parse(&self, line: &str) -> Result<Transaction>;
}

impl LogParser {
    pub fn new() -> Self {
        Self {
            format_detectors: vec![
                Box::new(JsonFormatDetector),
                Box::new(CsvFormatDetector),
                Box::new(FixedFormatDetector),
            ],
        }
    }

    pub fn parse_line(&self, line: &str) -> Result<Transaction> {
        for detector in &self.format_detectors {
            if detector.detect(line) {
                return detector.parse(line);
            }
        }

        Err(anyhow!("Unsupported log format: {}", line))
    }
}

/// JSON格式检测器
struct JsonFormatDetector;

impl FormatDetector for JsonFormatDetector {
    fn detect(&self, line: &str) -> bool {
        line.trim_start().starts_with('{') && line.trim_end().ends_with('}')
    }

    fn parse(&self, line: &str) -> Result<Transaction> {
        serde_json::from_str(line).map_err(|e| anyhow!("JSON parse error: {}", e))
    }
}

/// CSV格式检测器
struct CsvFormatDetector;

impl FormatDetector for CsvFormatDetector {
    fn detect(&self, line: &str) -> bool {
        line.contains(',') && line.split(',').count() >= 8
    }

    fn parse(&self, line: &str) -> Result<Transaction> {
        let fields: Vec<&str> = line.split(',').collect();
        if fields.len() < 8 {
            return Err(anyhow!("Invalid CSV format"));
        }

        Ok(Transaction {
            timestamp: fields[0].parse()?,
            macid: fields[1].to_string(),
            plate_no: if fields[2].is_empty() { None } else { Some(fields[2].to_string()) },
            vehicle_type: if fields[3].is_empty() { None } else { Some(fields[3].to_string()) },
            vehicle_fee_type: if fields[4].is_empty() { None } else { Some(fields[4].to_string()) },
            recievable_fee: if fields[5].is_empty() { None } else { Some(fields[5].parse()?) },
            actual_fee: if fields[6].is_empty() { None } else { Some(fields[6].parse()?) },
            pro_recievable_fee: if fields.len() > 7 && !fields[7].is_empty() { Some(fields[7].parse()?) } else { None },
            pro_actual_fee: if fields.len() > 8 && !fields[8].is_empty() { Some(fields[8].parse()?) } else { None },
        })
    }
}

/// 固定格式检测器
struct FixedFormatDetector;

impl FormatDetector for FixedFormatDetector {
    fn detect(&self, line: &str) -> bool {
        line.len() >= 100 && !line.contains(',') && !line.contains('{')
    }

    fn parse(&self, line: &str) -> Result<Transaction> {
        // 根据固定位置解析字段
        if line.len() < 100 {
            return Err(anyhow!("Line too short for fixed format"));
        }

        Ok(Transaction {
            timestamp: line[0..13].trim().parse()?,
            macid: line[13..33].trim().to_string(),
            plate_no: {
                let plate = line[33..41].trim();
                if plate.is_empty() { None } else { Some(plate.to_string()) }
            },
            vehicle_type: {
                let vtype = line[41..43].trim();
                if vtype.is_empty() { None } else { Some(vtype.to_string()) }
            },
            vehicle_fee_type: {
                let fee_type = line[43..45].trim();
                if fee_type.is_empty() { None } else { Some(fee_type.to_string()) }
            },
            recievable_fee: {
                let fee = line[45..55].trim();
                if fee.is_empty() { None } else { Some(fee.parse()?) }
            },
            actual_fee: {
                let fee = line[55..65].trim();
                if fee.is_empty() { None } else { Some(fee.parse()?) }
            },
            pro_recievable_fee: {
                let fee = line[65..75].trim();
                if fee.is_empty() { None } else { Some(fee.parse()?) }
            },
            pro_actual_fee: {
                let fee = line[75..85].trim();
                if fee.is_empty() { None } else { Some(fee.parse()?) }
            },
        })
    }
}
```

## 4. Image Crate详细设计

### 4.1 摄像头采集器设计

#### 4.1.1 功能描述
摄像头采集器通过厂商提供的SDK实时采集抓拍数据，支持多路摄像头并发采集。

#### 4.1.2 实现架构

```mermaid
stateDiagram-v2
    [*] --> 初始化
    初始化 --> SDK加载
    SDK加载 --> 设备发现
    设备发现 --> 设备连接
    设备连接 --> 数据订阅
    数据订阅 --> 实时采集
    实时采集 --> 数据处理
    数据处理 --> 实时采集

    SDK加载 --> 加载失败
    设备连接 --> 连接失败
    实时采集 --> 采集异常

    加载失败 --> 重试等待
    连接失败 --> 重连等待
    采集异常 --> 异常处理

    重试等待 --> SDK加载
    重连等待 --> 设备连接
    异常处理 --> 设备连接

    实时采集 --> [*] : 停止采集
```

#### 4.1.3 核心实现

```rust
// image/src/collector.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, mpsc};
use anyhow::{Result, Context};
use types::{Collector, CollectorStatus, NormalizedData, VehicleData};

pub struct CameraCollector {
    gantry_code: String,
    status: CollectorStatus,
    sdk_manager: Arc<Mutex<SdkManager>>,
    device_manager: Arc<Mutex<DeviceManager>>,
    stream_processor: Arc<Mutex<StreamProcessor>>,
    data_channel: Option<mpsc::Receiver<VehicleData>>,
}

impl CameraCollector {
    pub fn new(gantry_code: String, sdk_config: SdkConfig) -> Self {
        Self {
            gantry_code,
            status: CollectorStatus::Stopped,
            sdk_manager: Arc::new(Mutex::new(SdkManager::new(sdk_config))),
            device_manager: Arc::new(Mutex::new(DeviceManager::new())),
            stream_processor: Arc::new(Mutex::new(StreamProcessor::new())),
            data_channel: None,
        }
    }

    /// 初始化摄像头设备
    async fn initialize_devices(&mut self) -> Result<()> {
        let mut sdk_manager = self.sdk_manager.lock().await;
        sdk_manager.load_sdk().await?;

        let mut device_manager = self.device_manager.lock().await;
        let devices = sdk_manager.discover_devices().await?;

        for device_info in devices {
            device_manager.add_device(device_info).await?;
        }

        Ok(())
    }

    /// 启动数据流处理
    async fn start_data_streams(&mut self) -> Result<()> {
        let (tx, rx) = mpsc::channel(1000);
        self.data_channel = Some(rx);

        let device_manager = Arc::clone(&self.device_manager);
        let stream_processor = Arc::clone(&self.stream_processor);

        tokio::spawn(async move {
            let devices = device_manager.lock().await.get_all_devices();

            for device in devices {
                let tx_clone = tx.clone();
                let stream_processor_clone = Arc::clone(&stream_processor);

                tokio::spawn(async move {
                    Self::process_device_stream(device, tx_clone, stream_processor_clone).await;
                });
            }
        });

        Ok(())
    }

    /// 处理单个设备的数据流
    async fn process_device_stream(
        device: CameraDevice,
        tx: mpsc::Sender<VehicleData>,
        stream_processor: Arc<Mutex<StreamProcessor>>,
    ) {
        loop {
            match device.receive_data().await {
                Ok(raw_data) => {
                    let mut processor = stream_processor.lock().await;
                    if let Ok(vehicle_data) = processor.process_raw_data(raw_data).await {
                        if let Err(e) = tx.send(vehicle_data).await {
                            tracing::error!("Failed to send vehicle data: {}", e);
                            break;
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("Device {} error: {}", device.get_id(), e);
                    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;
                }
            }
        }
    }
}

impl Collector for CameraCollector {
    fn collect(&mut self) -> Result<Option<Vec<NormalizedData>>> {
        if let Some(ref mut rx) = self.data_channel {
            let mut results = Vec::new();

            // 非阻塞地接收所有可用数据
            while let Ok(vehicle_data) = rx.try_recv() {
                let normalized = NormalizedData::from_vehicle_data(
                    self.gantry_code.clone(),
                    vehicle_data
                );
                results.push(normalized);
            }

            if results.is_empty() {
                Ok(None)
            } else {
                Ok(Some(results))
            }
        } else {
            Ok(None)
        }
    }

    fn get_status(&self) -> CollectorStatus {
        self.status.clone()
    }

    fn start(&mut self) -> Result<()> {
        self.status = CollectorStatus::Starting;

        let rt = tokio::runtime::Handle::current();
        rt.block_on(async {
            self.initialize_devices().await?;
            self.start_data_streams().await?;
            Ok::<(), anyhow::Error>(())
        })?;

        self.status = CollectorStatus::Running;
        Ok(())
    }

    fn stop(&mut self) -> Result<()> {
        self.data_channel = None;
        self.status = CollectorStatus::Stopped;
        Ok(())
    }
}
```

### 4.2 SDK管理器设计

#### 4.2.1 功能描述
SDK管理器负责加载摄像头厂商的SDK库，提供统一的设备操作接口。

#### 4.2.2 核心实现

```rust
// image/src/sdk_manager.rs
use std::ffi::{CString, CStr};
use std::os::raw::{c_char, c_int, c_void};
use libloading::{Library, Symbol};
use anyhow::{Result, anyhow};

#[derive(Debug, Clone)]
pub struct SdkConfig {
    pub library_path: String,
    pub connection_timeout: u32,
    pub max_devices: u32,
}

pub struct SdkManager {
    config: SdkConfig,
    library: Option<Library>,
    is_initialized: bool,
}

// SDK函数指针类型定义
type InitSdkFn = unsafe extern "C" fn() -> c_int;
type CleanupSdkFn = unsafe extern "C" fn() -> c_int;
type DiscoverDevicesFn = unsafe extern "C" fn(*mut *mut DeviceInfo, *mut c_int) -> c_int;
type ConnectDeviceFn = unsafe extern "C" fn(*const c_char) -> *mut c_void;
type DisconnectDeviceFn = unsafe extern "C" fn(*mut c_void) -> c_int;
type StartStreamFn = unsafe extern "C" fn(*mut c_void, StreamCallback, *mut c_void) -> c_int;
type StopStreamFn = unsafe extern "C" fn(*mut c_void) -> c_int;

// 回调函数类型
type StreamCallback = unsafe extern "C" fn(*const RawImageData, *mut c_void);

#[repr(C)]
pub struct DeviceInfo {
    pub device_id: [c_char; 64],
    pub ip_address: [c_char; 16],
    pub port: c_int,
    pub device_type: c_int,
}

#[repr(C)]
pub struct RawImageData {
    pub timestamp: u64,
    pub camera_code: [c_char; 32],
    pub image_data: *const u8,
    pub image_size: u32,
    pub plate_info: PlateInfo,
    pub vehicle_info: VehicleInfo,
}

#[repr(C)]
pub struct PlateInfo {
    pub plate_no: [c_char; 16],
    pub plate_type: c_int,
    pub plate_color: c_int,
    pub confidence: f32,
}

#[repr(C)]
pub struct VehicleInfo {
    pub vehicle_type: c_int,
    pub vehicle_cat: c_int,
    pub vehicle_size: c_int,
    pub vehicle_color: c_int,
}

impl SdkManager {
    pub fn new(config: SdkConfig) -> Self {
        Self {
            config,
            library: None,
            is_initialized: false,
        }
    }

    /// 加载SDK库
    pub async fn load_sdk(&mut self) -> Result<()> {
        unsafe {
            let library = Library::new(&self.config.library_path)
                .context("Failed to load SDK library")?;

            // 获取初始化函数
            let init_fn: Symbol<InitSdkFn> = library.get(b"InitSDK")
                .context("Failed to find InitSDK function")?;

            // 调用初始化函数
            let result = init_fn();
            if result != 0 {
                return Err(anyhow!("SDK initialization failed with code: {}", result));
            }

            self.library = Some(library);
            self.is_initialized = true;

            tracing::info!("SDK loaded and initialized successfully");
            Ok(())
        }
    }

    /// 发现设备
    pub async fn discover_devices(&self) -> Result<Vec<CameraDeviceInfo>> {
        if !self.is_initialized {
            return Err(anyhow!("SDK not initialized"));
        }

        unsafe {
            let library = self.library.as_ref().unwrap();
            let discover_fn: Symbol<DiscoverDevicesFn> = library.get(b"DiscoverDevices")
                .context("Failed to find DiscoverDevices function")?;

            let mut device_list: *mut DeviceInfo = std::ptr::null_mut();
            let mut device_count: c_int = 0;

            let result = discover_fn(&mut device_list, &mut device_count);
            if result != 0 {
                return Err(anyhow!("Device discovery failed with code: {}", result));
            }

            let mut devices = Vec::new();
            for i in 0..device_count {
                let device_info = &*device_list.offset(i as isize);
                devices.push(CameraDeviceInfo::from_raw(device_info));
            }

            // 释放SDK分配的内存
            libc::free(device_list as *mut c_void);

            tracing::info!("Discovered {} camera devices", devices.len());
            Ok(devices)
        }
    }

    /// 连接设备
    pub async fn connect_device(&self, device_id: &str) -> Result<*mut c_void> {
        if !self.is_initialized {
            return Err(anyhow!("SDK not initialized"));
        }

        unsafe {
            let library = self.library.as_ref().unwrap();
            let connect_fn: Symbol<ConnectDeviceFn> = library.get(b"ConnectDevice")
                .context("Failed to find ConnectDevice function")?;

            let device_id_cstr = CString::new(device_id)?;
            let handle = connect_fn(device_id_cstr.as_ptr());

            if handle.is_null() {
                return Err(anyhow!("Failed to connect to device: {}", device_id));
            }

            tracing::info!("Connected to device: {}", device_id);
            Ok(handle)
        }
    }

    /// 启动数据流
    pub async fn start_stream(
        &self,
        device_handle: *mut c_void,
        callback: StreamCallback,
        user_data: *mut c_void,
    ) -> Result<()> {
        if !self.is_initialized {
            return Err(anyhow!("SDK not initialized"));
        }

        unsafe {
            let library = self.library.as_ref().unwrap();
            let start_stream_fn: Symbol<StartStreamFn> = library.get(b"StartStream")
                .context("Failed to find StartStream function")?;

            let result = start_stream_fn(device_handle, callback, user_data);
            if result != 0 {
                return Err(anyhow!("Failed to start stream with code: {}", result));
            }

            tracing::info!("Stream started successfully");
            Ok(())
        }
    }
}

impl Drop for SdkManager {
    fn drop(&mut self) {
        if self.is_initialized {
            unsafe {
                if let Some(ref library) = self.library {
                    if let Ok(cleanup_fn) = library.get::<Symbol<CleanupSdkFn>>(b"CleanupSDK") {
                        cleanup_fn();
                        tracing::info!("SDK cleaned up");
                    }
                }
            }
        }
    }
}

#[derive(Debug, Clone)]
pub struct CameraDeviceInfo {
    pub device_id: String,
    pub ip_address: String,
    pub port: u16,
    pub device_type: u32,
}

impl CameraDeviceInfo {
    fn from_raw(raw: &DeviceInfo) -> Self {
        unsafe {
            Self {
                device_id: CStr::from_ptr(raw.device_id.as_ptr()).to_string_lossy().to_string(),
                ip_address: CStr::from_ptr(raw.ip_address.as_ptr()).to_string_lossy().to_string(),
                port: raw.port as u16,
                device_type: raw.device_type as u32,
            }
        }
    }
}
```

### 4.3 设备管理器设计

#### 4.3.1 功能描述
设备管理器负责管理多个摄像头设备的连接状态、配置信息和生命周期。

#### 4.3.2 核心实现

```rust
// image/src/device_manager.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use anyhow::{Result, anyhow};

pub struct DeviceManager {
    devices: HashMap<String, CameraDevice>,
    device_configs: HashMap<String, DeviceConfig>,
}

#[derive(Debug, Clone)]
pub struct DeviceConfig {
    pub camera_code: String,
    pub ip: String,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub max_reconnect_attempts: u32,
    pub reconnect_interval: u64,
}

pub struct CameraDevice {
    pub device_id: String,
    pub config: DeviceConfig,
    pub handle: Option<*mut std::ffi::c_void>,
    pub status: DeviceStatus,
    pub reconnect_attempts: u32,
    pub last_data_time: Option<std::time::Instant>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum DeviceStatus {
    Disconnected,
    Connecting,
    Connected,
    Streaming,
    Error(String),
}

impl DeviceManager {
    pub fn new() -> Self {
        Self {
            devices: HashMap::new(),
            device_configs: HashMap::new(),
        }
    }

    /// 添加设备配置
    pub async fn add_device(&mut self, device_info: CameraDeviceInfo) -> Result<()> {
        let config = DeviceConfig {
            camera_code: device_info.device_id.clone(),
            ip: device_info.ip_address,
            port: device_info.port,
            username: "admin".to_string(), // 从配置文件读取
            password: "password".to_string(), // 从配置文件读取
            max_reconnect_attempts: 3,
            reconnect_interval: 30,
        };

        let device = CameraDevice {
            device_id: device_info.device_id.clone(),
            config: config.clone(),
            handle: None,
            status: DeviceStatus::Disconnected,
            reconnect_attempts: 0,
            last_data_time: None,
        };

        self.device_configs.insert(device_info.device_id.clone(), config);
        self.devices.insert(device_info.device_id, device);

        Ok(())
    }

    /// 连接所有设备
    pub async fn connect_all_devices(&mut self, sdk_manager: &SdkManager) -> Result<()> {
        for (device_id, device) in self.devices.iter_mut() {
            if device.status == DeviceStatus::Disconnected {
                match self.connect_device(device_id, sdk_manager).await {
                    Ok(_) => {
                        tracing::info!("Device {} connected successfully", device_id);
                    }
                    Err(e) => {
                        tracing::error!("Failed to connect device {}: {}", device_id, e);
                        device.status = DeviceStatus::Error(e.to_string());
                    }
                }
            }
        }
        Ok(())
    }

    /// 连接单个设备
    async fn connect_device(&mut self, device_id: &str, sdk_manager: &SdkManager) -> Result<()> {
        if let Some(device) = self.devices.get_mut(device_id) {
            device.status = DeviceStatus::Connecting;

            match sdk_manager.connect_device(device_id).await {
                Ok(handle) => {
                    device.handle = Some(handle);
                    device.status = DeviceStatus::Connected;
                    device.reconnect_attempts = 0;
                    Ok(())
                }
                Err(e) => {
                    device.status = DeviceStatus::Error(e.to_string());
                    Err(e)
                }
            }
        } else {
            Err(anyhow!("Device not found: {}", device_id))
        }
    }

    /// 获取所有设备
    pub fn get_all_devices(&self) -> Vec<CameraDevice> {
        self.devices.values().cloned().collect()
    }

    /// 获取连接的设备
    pub fn get_connected_devices(&self) -> Vec<&CameraDevice> {
        self.devices.values()
            .filter(|device| matches!(device.status, DeviceStatus::Connected | DeviceStatus::Streaming))
            .collect()
    }

    /// 检查设备健康状态
    pub async fn check_device_health(&mut self) -> Result<()> {
        let now = std::time::Instant::now();

        for (device_id, device) in self.devices.iter_mut() {
            // 检查设备是否长时间无数据
            if let Some(last_data_time) = device.last_data_time {
                if now.duration_since(last_data_time).as_secs() > 300 { // 5分钟无数据
                    tracing::warn!("Device {} has no data for 5 minutes", device_id);
                    device.status = DeviceStatus::Error("No data timeout".to_string());
                }
            }
        }

        Ok(())
    }

    /// 更新设备最后数据时间
    pub fn update_device_data_time(&mut self, device_id: &str) {
        if let Some(device) = self.devices.get_mut(device_id) {
            device.last_data_time = Some(std::time::Instant::now());
            if device.status == DeviceStatus::Connected {
                device.status = DeviceStatus::Streaming;
            }
        }
    }
}

impl CameraDevice {
    pub fn get_id(&self) -> &str {
        &self.device_id
    }

    pub fn get_handle(&self) -> Option<*mut std::ffi::c_void> {
        self.handle
    }

    pub fn is_connected(&self) -> bool {
        matches!(self.status, DeviceStatus::Connected | DeviceStatus::Streaming)
    }

    /// 接收设备数据（模拟异步接收）
    pub async fn receive_data(&self) -> Result<RawImageData> {
        if !self.is_connected() {
            return Err(anyhow!("Device not connected"));
        }

        // 这里应该是从SDK回调中接收数据
        // 为了演示，我们模拟一个异步等待
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        // 模拟接收到的原始数据
        Ok(RawImageData {
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            camera_code: [0; 32], // 实际应该填充设备ID
            image_data: std::ptr::null(),
            image_size: 0,
            plate_info: PlateInfo {
                plate_no: [0; 16],
                plate_type: 0,
                plate_color: 0,
                confidence: 0.0,
            },
            vehicle_info: VehicleInfo {
                vehicle_type: 0,
                vehicle_cat: 0,
                vehicle_size: 0,
                vehicle_color: 0,
            },
        })
    }
}
```

### 4.4 流处理器设计

#### 4.4.1 功能描述
流处理器负责处理从SDK接收到的原始数据，转换为标准的VehicleData格式。

#### 4.4.2 核心实现

```rust
// image/src/stream_processor.rs
use anyhow::{Result, anyhow};
use types::VehicleData;
use std::ffi::CStr;

pub struct StreamProcessor {
    image_quality_threshold: f32,
    max_image_size: usize,
}

impl StreamProcessor {
    pub fn new() -> Self {
        Self {
            image_quality_threshold: 0.7, // 图像质量阈值
            max_image_size: 5 * 1024 * 1024, // 5MB最大图像大小
        }
    }

    /// 处理原始图像数据
    pub async fn process_raw_data(&mut self, raw_data: RawImageData) -> Result<VehicleData> {
        // 验证数据质量
        self.validate_raw_data(&raw_data)?;

        // 提取摄像头编码
        let camera_code = self.extract_camera_code(&raw_data)?;

        // 提取车牌信息
        let (plate_no, plate_type, plate_color) = self.extract_plate_info(&raw_data.plate_info)?;

        // 提取车辆信息
        let (vehicle_type, vehicle_cat, vehicle_size, vehicle_object) =
            self.extract_vehicle_info(&raw_data.vehicle_info)?;

        // 处理图像数据
        let image_bytes = self.process_image_data(&raw_data).await?;

        Ok(VehicleData {
            timestamp: raw_data.timestamp,
            camera_code,
            plate_no,
            plate_type,
            plate_color,
            vehicle_type,
            vehicle_cat,
            vehicle_size,
            vehicle_object,
            image_bytes,
        })
    }

    /// 验证原始数据
    fn validate_raw_data(&self, raw_data: &RawImageData) -> Result<()> {
        // 检查时间戳
        if raw_data.timestamp == 0 {
            return Err(anyhow!("Invalid timestamp"));
        }

        // 检查图像大小
        if raw_data.image_size > self.max_image_size as u32 {
            return Err(anyhow!("Image size too large: {}", raw_data.image_size));
        }

        // 检查车牌置信度
        if raw_data.plate_info.confidence < self.image_quality_threshold {
            return Err(anyhow!("Plate confidence too low: {}", raw_data.plate_info.confidence));
        }

        Ok(())
    }

    /// 提取摄像头编码
    fn extract_camera_code(&self, raw_data: &RawImageData) -> Result<String> {
        unsafe {
            let camera_code = CStr::from_ptr(raw_data.camera_code.as_ptr())
                .to_string_lossy()
                .to_string();

            if camera_code.is_empty() {
                return Err(anyhow!("Empty camera code"));
            }

            Ok(camera_code)
        }
    }

    /// 提取车牌信息
    fn extract_plate_info(&self, plate_info: &PlateInfo) -> Result<(Option<String>, Option<String>, Option<String>)> {
        unsafe {
            let plate_no = CStr::from_ptr(plate_info.plate_no.as_ptr())
                .to_string_lossy()
                .to_string();

            let plate_no = if plate_no.is_empty() { None } else { Some(plate_no) };

            let plate_type = match plate_info.plate_type {
                0 => None,
                1 => Some("蓝牌".to_string()),
                2 => Some("黄牌".to_string()),
                3 => Some("白牌".to_string()),
                4 => Some("黑牌".to_string()),
                5 => Some("绿牌".to_string()),
                _ => Some("未知".to_string()),
            };

            let plate_color = match plate_info.plate_color {
                0 => None,
                1 => Some("蓝色".to_string()),
                2 => Some("黄色".to_string()),
                3 => Some("白色".to_string()),
                4 => Some("黑色".to_string()),
                5 => Some("绿色".to_string()),
                _ => Some("未知".to_string()),
            };

            Ok((plate_no, plate_type, plate_color))
        }
    }

    /// 提取车辆信息
    fn extract_vehicle_info(&self, vehicle_info: &VehicleInfo) -> Result<(Option<String>, Option<String>, Option<String>, Option<String>)> {
        let vehicle_type = match vehicle_info.vehicle_type {
            0 => None,
            1 => Some("小型车".to_string()),
            2 => Some("中型车".to_string()),
            3 => Some("大型车".to_string()),
            4 => Some("特大型车".to_string()),
            _ => Some("未知".to_string()),
        };

        let vehicle_cat = match vehicle_info.vehicle_cat {
            0 => None,
            1 => Some("客车".to_string()),
            2 => Some("货车".to_string()),
            3 => Some("专项作业车".to_string()),
            _ => Some("未知".to_string()),
        };

        let vehicle_size = match vehicle_info.vehicle_size {
            0 => None,
            1 => Some("小".to_string()),
            2 => Some("中".to_string()),
            3 => Some("大".to_string()),
            _ => Some("未知".to_string()),
        };

        // 组合车辆对象信息
        let vehicle_object = if vehicle_type.is_some() || vehicle_cat.is_some() {
            Some(format!("{}-{}",
                vehicle_type.as_deref().unwrap_or("未知"),
                vehicle_cat.as_deref().unwrap_or("未知")
            ))
        } else {
            None
        };

        Ok((vehicle_type, vehicle_cat, vehicle_size, vehicle_object))
    }

    /// 处理图像数据
    async fn process_image_data(&self, raw_data: &RawImageData) -> Result<Option<Vec<u8>>> {
        if raw_data.image_data.is_null() || raw_data.image_size == 0 {
            return Ok(None);
        }

        unsafe {
            let image_slice = std::slice::from_raw_parts(
                raw_data.image_data,
                raw_data.image_size as usize
            );

            // 复制图像数据
            let mut image_bytes = Vec::with_capacity(raw_data.image_size as usize);
            image_bytes.extend_from_slice(image_slice);

            // 可以在这里添加图像压缩、格式转换等处理
            self.compress_image(&mut image_bytes).await?;

            Ok(Some(image_bytes))
        }
    }

    /// 压缩图像
    async fn compress_image(&self, image_bytes: &mut Vec<u8>) -> Result<()> {
        // 这里可以实现图像压缩逻辑
        // 例如使用JPEG压缩、调整分辨率等

        // 模拟异步压缩处理
        tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;

        // 简单的大小检查，如果图像太大则进行压缩
        if image_bytes.len() > 1024 * 1024 { // 1MB
            // 这里应该实现实际的压缩算法
            tracing::info!("Image compressed from {} bytes", image_bytes.len());
        }

        Ok(())
    }
}
```

## 5. 配置管理

### 5.1 配置文件结构

```yaml
# config/collectors.yaml
collectors:
  transaction:
    enabled: true
    passive:
      enabled: true
      watch_directory: "/data/logs"
      file_pattern: "*.log"
      buffer_size: 1000
    sftp:
      enabled: false
      host: "*************"
      port: 22
      username: "loguser"
      password: "${SFTP_PASSWORD}"
      remote_directory: "/logs"
      file_pattern: "transaction_*.log"
      sync_interval: 300  # seconds

  image:
    enabled: true
    sdk_config:
      library_path: "/usr/lib/camera_sdk.so"
      connection_timeout: 30
      max_devices: 10
    devices:
      - camera_code: "cam001"
        ip: "*************"
        port: 8000
        username: "admin"
        password: "${CAM001_PASSWORD}"
      - camera_code: "cam002"
        ip: "*************"
        port: 8000
        username: "admin"
        password: "${CAM002_PASSWORD}"

# 通用配置
common:
  gantry_code: "G001"
  data_buffer_size: 10000
  max_memory_usage: "2GB"
  log_level: "info"
```

## 6. 性能优化

### 6.1 采集性能优化策略

1. **异步I/O**: 使用Tokio异步运行时，避免阻塞操作
2. **批量处理**: 批量读取和处理数据，减少系统调用开销
3. **内存池**: 复用内存缓冲区，减少内存分配和释放
4. **连接复用**: 复用网络连接，减少连接建立开销
5. **数据压缩**: 对图像数据进行压缩，减少内存占用

### 6.2 并发控制

1. **线程池**: 使用固定大小的线程池处理CPU密集型任务
2. **信号量**: 控制并发连接数，避免资源耗尽
3. **背压控制**: 当下游处理不及时时，控制上游数据产生速度
4. **优雅关闭**: 确保在系统关闭时正确释放资源

## 7. 错误处理和监控

### 7.1 错误分类和处理策略

| 错误类型 | 处理策略 | 恢复机制 |
|---------|---------|---------|
| 网络连接错误 | 自动重连 | 指数退避重试 |
| 文件读取错误 | 跳过损坏文件 | 记录错误日志 |
| SDK调用错误 | 重启SDK | 切换备用设备 |
| 数据格式错误 | 丢弃错误数据 | 记录解析错误 |
| 内存不足错误 | 清理缓存 | 触发垃圾回收 |

### 7.2 监控指标

1. **采集指标**: 采集速率、成功率、延迟
2. **设备指标**: 设备连接状态、数据质量
3. **系统指标**: CPU使用率、内存使用率、网络I/O
4. **错误指标**: 错误率、错误类型分布

## 8. 总结

数据采集模块是整个系统的数据入口，其设计质量直接影响系统的整体性能和可靠性。本设计文档详细描述了：

1. **模块化架构**: Transaction和Image两个独立的采集模块，职责清晰
2. **统一接口**: 通过Collector trait提供统一的采集接口
3. **多种数据源支持**: 支持被动文件监控、主动SFTP下载、摄像头SDK等多种数据源
4. **高性能设计**: 异步处理、批量操作、内存优化等性能优化策略
5. **可靠性保障**: 错误处理、自动重连、健康检查等可靠性机制
6. **可扩展性**: 支持新的数据源类型和采集方式的扩展

该设计为后续的数据处理、传输和存储模块提供了稳定可靠的数据来源。
