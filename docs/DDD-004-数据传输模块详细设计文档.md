# DDD-004 数据传输模块详细设计文档

**文档编号：** DDD-004  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 数据传输团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md  
**上级设计：** DDD-001-系统总体架构详细设计文档.md

---

## 1. 模块概述

### 1.1 模块目的
数据传输模块负责将处理后的数据通过多种通信方式可靠地传输到外部系统，支持串口通信和TCP/IP网络通信，确保数据传输的完整性和实时性。

### 1.2 需求追溯
本模块对应PRD-001中的以下需求：
- **FR-003**: 数据传输功能
  - 串口传输：通过RS232/RS485协议传输数据
  - 网络传输：通过TCP/IP协议传输数据
- **性能要求**: 数据传输延迟≤1秒，支持高并发传输
- **可靠性要求**: 传输成功率≥99.9%，支持自动重连和重传

### 1.3 模块职责
- **串口传输**: 通过串口设备传输数据到外部系统
- **网络传输**: 通过TCP/UDP协议传输数据到远程服务器
- **传输管理**: 连接管理、重连机制、传输队列管理
- **协议处理**: 数据帧封装、校验、确认机制
- **监控统计**: 传输状态监控、性能统计、错误处理

## 2. 模块架构设计

### 2.1 传输架构图

```mermaid
graph TB
    subgraph "数据传输模块 (Transport Crate)"
        subgraph "传输接口层"
            TI1[DataTransport Trait<br/>传输接口定义]
            TI2[TransportManager<br/>传输管理器]
        end
        
        subgraph "串口传输层"
            ST1[SerialTransport<br/>串口传输器]
            ST2[SerialConnection<br/>串口连接管理]
            ST3[SerialProtocol<br/>串口协议处理]
        end
        
        subgraph "网络传输层"
            NT1[TcpTransport<br/>TCP传输器]
            NT2[UdpTransport<br/>UDP传输器]
            NT3[NetworkConnection<br/>网络连接管理]
            NT4[NetworkProtocol<br/>网络协议处理]
        end
        
        subgraph "传输支持层"
            TS1[TransmissionQueue<br/>传输队列]
            TS2[RetryManager<br/>重试管理器]
            TS3[ConnectionPool<br/>连接池]
            TS4[ProtocolEncoder<br/>协议编码器]
        end
        
        subgraph "监控统计层"
            MS1[TransportMetrics<br/>传输指标]
            MS2[HealthChecker<br/>健康检查器]
            MS3[AlertManager<br/>告警管理器]
        end
    end
    
    subgraph "外部系统"
        EXT1[串口设备]
        EXT2[远程TCP服务器]
        EXT3[UDP接收端]
        EXT4[监控系统]
    end

    TI2 --> ST1
    TI2 --> NT1
    TI2 --> NT2
    
    ST1 --> ST2
    ST1 --> ST3
    ST2 --> EXT1
    
    NT1 --> NT3
    NT1 --> NT4
    NT2 --> NT3
    NT2 --> NT4
    NT3 --> EXT2
    NT3 --> EXT3
    
    ST1 --> TS1
    NT1 --> TS1
    NT2 --> TS1
    
    TS1 --> TS2
    ST2 --> TS3
    NT3 --> TS3
    
    ST3 --> TS4
    NT4 --> TS4
    
    TI2 --> MS1
    MS1 --> MS2
    MS2 --> MS3
    MS3 --> EXT4
```

### 2.2 传输流程图

```mermaid
sequenceDiagram
    participant App as 主应用
    participant TM as TransportManager
    participant Queue as TransmissionQueue
    participant Transport as DataTransport
    participant External as 外部系统

    App->>TM: 发送数据请求
    TM->>Queue: 数据入队
    Queue->>TM: 返回队列位置
    TM->>App: 确认接收

    loop 传输处理
        Queue->>Transport: 获取待传输数据
        Transport->>Transport: 数据编码和封装
        Transport->>External: 发送数据
        
        alt 传输成功
            External->>Transport: 返回确认
            Transport->>Queue: 标记传输成功
        else 传输失败
            Transport->>Queue: 标记传输失败
            Queue->>Queue: 重试计数+1
            
            alt 未达到最大重试次数
                Queue->>Transport: 重新传输
            else 达到最大重试次数
                Queue->>TM: 传输失败通知
                TM->>App: 错误回调
            end
        end
    end
```

## 3. 核心接口设计

### 3.1 传输接口定义

```rust
// transport/src/lib.rs
use async_trait::async_trait;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use types::NormalizedData;

/// 数据传输接口
#[async_trait]
pub trait DataTransport: Send + Sync {
    /// 建立连接
    async fn connect(&mut self) -> Result<()>;

    /// 断开连接
    async fn disconnect(&mut self) -> Result<()>;

    /// 发送单条数据
    async fn send(&mut self, data: &NormalizedData) -> Result<TransmissionResult>;

    /// 批量发送数据
    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<Vec<TransmissionResult>>;

    /// 获取传输状态
    async fn get_status(&self) -> TransportStatus;

    /// 获取传输类型
    fn get_transport_type(&self) -> TransportType;

    /// 获取传输统计信息
    fn get_statistics(&self) -> TransportStatistics;
}

/// 传输结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransmissionResult {
    pub success: bool,
    pub message_id: String,
    pub timestamp: u64,
    pub bytes_sent: usize,
    pub response_time_ms: u64,
    pub error: Option<TransportError>,
}

/// 传输状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportStatus {
    Disconnected,
    Connecting,
    Connected,
    Transmitting,
    Error(String),
}

/// 传输类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportType {
    Serial { port: String, baud_rate: u32 },
    TcpClient { host: String, port: u16 },
    TcpServer { bind_address: String, port: u16 },
    Udp { host: String, port: u16 },
}

/// 传输统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransportStatistics {
    pub total_sent: u64,
    pub success_count: u64,
    pub error_count: u64,
    pub bytes_sent: u64,
    pub average_response_time_ms: f64,
    pub throughput_per_second: f64,
    pub last_error: Option<TransportError>,
}

/// 传输错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportError {
    ConnectionFailed(String),
    SendFailed(String),
    Timeout,
    ProtocolError(String),
    NetworkError(String),
    SerialError(String),
}
```

### 3.2 传输管理器接口

```rust
// transport/src/manager.rs
use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;

pub struct TransportManager {
    transports: HashMap<String, Arc<Mutex<dyn DataTransport>>>,
    transmission_queue: Arc<Mutex<TransmissionQueue>>,
    retry_manager: Arc<Mutex<RetryManager>>,
    config: TransportConfig,
    metrics: Arc<TransportMetrics>,
}

#[derive(Debug, Clone)]
pub struct TransportConfig {
    pub max_queue_size: usize,
    pub max_retry_attempts: u32,
    pub retry_interval_ms: u64,
    pub batch_size: usize,
    pub transmission_timeout_ms: u64,
}

impl TransportManager {
    pub fn new(config: TransportConfig) -> Self {
        Self {
            transports: HashMap::new(),
            transmission_queue: Arc::new(Mutex::new(TransmissionQueue::new(config.max_queue_size))),
            retry_manager: Arc::new(Mutex::new(RetryManager::new(config.max_retry_attempts))),
            config,
            metrics: Arc::new(TransportMetrics::new()),
        }
    }

    /// 注册传输器
    pub async fn register_transport(&mut self, name: String, transport: Arc<Mutex<dyn DataTransport>>) -> Result<()> {
        self.transports.insert(name, transport);
        Ok(())
    }

    /// 发送数据到指定传输器
    pub async fn send_to(&self, transport_name: &str, data: NormalizedData) -> Result<String> {
        let message_id = uuid::Uuid::new_v4().to_string();
        
        let transmission_item = TransmissionItem {
            id: message_id.clone(),
            transport_name: transport_name.to_string(),
            data,
            created_at: chrono::Utc::now().timestamp_millis() as u64,
            retry_count: 0,
            max_retries: self.config.max_retry_attempts,
        };

        let mut queue = self.transmission_queue.lock().await;
        queue.enqueue(transmission_item).await?;

        Ok(message_id)
    }

    /// 发送数据到所有传输器
    pub async fn broadcast(&self, data: NormalizedData) -> Result<Vec<String>> {
        let mut message_ids = Vec::new();
        
        for transport_name in self.transports.keys() {
            let message_id = self.send_to(transport_name, data.clone()).await?;
            message_ids.push(message_id);
        }

        Ok(message_ids)
    }

    /// 启动传输处理循环
    pub async fn start_transmission_loop(&self) -> Result<()> {
        let queue = Arc::clone(&self.transmission_queue);
        let transports = self.transports.clone();
        let retry_manager = Arc::clone(&self.retry_manager);
        let metrics = Arc::clone(&self.metrics);
        let config = self.config.clone();

        tokio::spawn(async move {
            loop {
                let mut queue_guard = queue.lock().await;
                if let Some(item) = queue_guard.dequeue().await {
                    drop(queue_guard);

                    if let Some(transport) = transports.get(&item.transport_name) {
                        let start_time = std::time::Instant::now();
                        let mut transport_guard = transport.lock().await;
                        
                        match transport_guard.send(&item.data).await {
                            Ok(result) => {
                                metrics.record_success(start_time.elapsed().as_millis() as u64, result.bytes_sent);
                                tracing::info!("Successfully sent message {}", item.id);
                            }
                            Err(e) => {
                                metrics.record_error();
                                tracing::error!("Failed to send message {}: {}", item.id, e);
                                
                                // 处理重试
                                let mut retry_guard = retry_manager.lock().await;
                                if retry_guard.should_retry(&item) {
                                    let mut retry_item = item;
                                    retry_item.retry_count += 1;
                                    
                                    tokio::time::sleep(tokio::time::Duration::from_millis(config.retry_interval_ms)).await;
                                    
                                    let mut queue_guard = queue.lock().await;
                                    if let Err(e) = queue_guard.enqueue(retry_item).await {
                                        tracing::error!("Failed to requeue message for retry: {}", e);
                                    }
                                }
                            }
                        }
                    }
                } else {
                    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                }
            }
        });

        Ok(())
    }
}
```

## 4. 串口传输实现

### 4.1 串口传输器

```rust
// transport/src/serial.rs
use async_trait::async_trait;
use anyhow::{Result, anyhow};
use tokio_serial::{SerialPort, SerialPortBuilderExt};
use std::time::{Duration, Instant};
use crc::{Crc, CRC_16_IBM_SDLC};

pub struct SerialTransport {
    config: SerialConfig,
    port: Option<Box<dyn SerialPort>>,
    status: TransportStatus,
    statistics: TransportStatistics,
    protocol: SerialProtocol,
}

#[derive(Debug, Clone)]
pub struct SerialConfig {
    pub port_name: String,
    pub baud_rate: u32,
    pub data_bits: tokio_serial::DataBits,
    pub stop_bits: tokio_serial::StopBits,
    pub parity: tokio_serial::Parity,
    pub flow_control: tokio_serial::FlowControl,
    pub timeout: Duration,
}

impl SerialTransport {
    pub fn new(config: SerialConfig) -> Self {
        Self {
            config,
            port: None,
            status: TransportStatus::Disconnected,
            statistics: TransportStatistics::default(),
            protocol: SerialProtocol::new(),
        }
    }
}

#[async_trait]
impl DataTransport for SerialTransport {
    async fn connect(&mut self) -> Result<()> {
        self.status = TransportStatus::Connecting;

        let port = tokio_serial::new(&self.config.port_name, self.config.baud_rate)
            .data_bits(self.config.data_bits)
            .stop_bits(self.config.stop_bits)
            .parity(self.config.parity)
            .flow_control(self.config.flow_control)
            .timeout(self.config.timeout)
            .open()?;

        self.port = Some(port);
        self.status = TransportStatus::Connected;
        
        tracing::info!("Serial port {} connected successfully", self.config.port_name);
        Ok(())
    }

    async fn disconnect(&mut self) -> Result<()> {
        self.port = None;
        self.status = TransportStatus::Disconnected;
        
        tracing::info!("Serial port {} disconnected", self.config.port_name);
        Ok(())
    }

    async fn send(&mut self, data: &NormalizedData) -> Result<TransmissionResult> {
        let start_time = Instant::now();
        
        if self.port.is_none() {
            return Err(anyhow!("Serial port not connected"));
        }

        self.status = TransportStatus::Transmitting;

        // 编码数据
        let frame = self.protocol.encode_data(data)?;
        let message_id = uuid::Uuid::new_v4().to_string();

        // 发送数据
        let port = self.port.as_mut().unwrap();
        let bytes_sent = self.send_frame(port, &frame).await?;

        // 等待确认（如果需要）
        let response_time = start_time.elapsed().as_millis() as u64;

        self.status = TransportStatus::Connected;
        self.statistics.total_sent += 1;
        self.statistics.success_count += 1;
        self.statistics.bytes_sent += bytes_sent as u64;

        Ok(TransmissionResult {
            success: true,
            message_id,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            bytes_sent,
            response_time_ms: response_time,
            error: None,
        })
    }

    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<Vec<TransmissionResult>> {
        let mut results = Vec::new();
        
        for item in data {
            match self.send(item).await {
                Ok(result) => results.push(result),
                Err(e) => {
                    results.push(TransmissionResult {
                        success: false,
                        message_id: uuid::Uuid::new_v4().to_string(),
                        timestamp: chrono::Utc::now().timestamp_millis() as u64,
                        bytes_sent: 0,
                        response_time_ms: 0,
                        error: Some(TransportError::SendFailed(e.to_string())),
                    });
                }
            }
        }

        Ok(results)
    }

    async fn get_status(&self) -> TransportStatus {
        self.status.clone()
    }

    fn get_transport_type(&self) -> TransportType {
        TransportType::Serial {
            port: self.config.port_name.clone(),
            baud_rate: self.config.baud_rate,
        }
    }

    fn get_statistics(&self) -> TransportStatistics {
        self.statistics.clone()
    }
}

impl SerialTransport {
    async fn send_frame(&mut self, port: &mut Box<dyn SerialPort>, frame: &[u8]) -> Result<usize> {
        use tokio::io::AsyncWriteExt;
        
        port.write_all(frame).await?;
        port.flush().await?;
        
        Ok(frame.len())
    }
}
```

### 4.2 串口协议处理

```rust
// transport/src/serial_protocol.rs
use anyhow::{Result, anyhow};
use types::NormalizedData;
use crc::{Crc, CRC_16_IBM_SDLC};

pub struct SerialProtocol {
    crc: Crc<u16>,
}

impl SerialProtocol {
    pub fn new() -> Self {
        Self {
            crc: Crc::<u16>::new(&CRC_16_IBM_SDLC),
        }
    }

    /// 编码数据为串口帧格式
    pub fn encode_data(&self, data: &NormalizedData) -> Result<Vec<u8>> {
        // 序列化数据为JSON
        let json_data = serde_json::to_vec(data)?;

        // 构建数据帧
        let mut frame = Vec::new();

        // 帧头 (2字节)
        frame.extend_from_slice(&[0xAA, 0x55]);

        // 数据长度 (4字节，大端序)
        let data_length = json_data.len() as u32;
        frame.extend_from_slice(&data_length.to_be_bytes());

        // 数据内容
        frame.extend_from_slice(&json_data);

        // 计算校验码
        let checksum = self.crc.checksum(&frame[2..]); // 不包含帧头的校验
        frame.extend_from_slice(&checksum.to_be_bytes());

        // 帧尾 (2字节)
        frame.extend_from_slice(&[0x55, 0xAA]);

        Ok(frame)
    }

    /// 解码串口帧数据
    pub fn decode_frame(&self, frame: &[u8]) -> Result<NormalizedData> {
        if frame.len() < 10 { // 最小帧长度：帧头(2) + 长度(4) + 校验(2) + 帧尾(2)
            return Err(anyhow!("Frame too short"));
        }

        // 检查帧头
        if frame[0] != 0xAA || frame[1] != 0x55 {
            return Err(anyhow!("Invalid frame header"));
        }

        // 检查帧尾
        let frame_end = frame.len() - 2;
        if frame[frame_end] != 0x55 || frame[frame_end + 1] != 0xAA {
            return Err(anyhow!("Invalid frame tail"));
        }

        // 提取数据长度
        let data_length = u32::from_be_bytes([frame[2], frame[3], frame[4], frame[5]]) as usize;

        if frame.len() != 10 + data_length {
            return Err(anyhow!("Frame length mismatch"));
        }

        // 提取数据内容
        let data_start = 6;
        let data_end = data_start + data_length;
        let json_data = &frame[data_start..data_end];

        // 验证校验码
        let expected_checksum = u16::from_be_bytes([frame[data_end], frame[data_end + 1]]);
        let actual_checksum = self.crc.checksum(&frame[2..data_end]);

        if expected_checksum != actual_checksum {
            return Err(anyhow!("Checksum verification failed"));
        }

        // 反序列化数据
        let normalized_data: NormalizedData = serde_json::from_slice(json_data)?;
        Ok(normalized_data)
    }
}
```

## 5. 网络传输实现

### 5.1 TCP传输器

```rust
// transport/src/tcp.rs
use async_trait::async_trait;
use anyhow::{Result, anyhow};
use tokio::net::{TcpStream, TcpListener};
use tokio::io::{AsyncReadExt, AsyncWriteExt};
use std::time::{Duration, Instant};
use std::sync::Arc;
use tokio::sync::Mutex;

pub struct TcpTransport {
    config: TcpConfig,
    connection: Option<Arc<Mutex<TcpStream>>>,
    status: TransportStatus,
    statistics: TransportStatistics,
    protocol: NetworkProtocol,
    mode: TcpMode,
}

#[derive(Debug, Clone)]
pub struct TcpConfig {
    pub host: String,
    pub port: u16,
    pub connect_timeout: Duration,
    pub read_timeout: Duration,
    pub write_timeout: Duration,
    pub keep_alive: bool,
    pub no_delay: bool,
}

#[derive(Debug, Clone)]
pub enum TcpMode {
    Client,
    Server { bind_address: String },
}

impl TcpTransport {
    pub fn new_client(config: TcpConfig) -> Self {
        Self {
            config,
            connection: None,
            status: TransportStatus::Disconnected,
            statistics: TransportStatistics::default(),
            protocol: NetworkProtocol::new(),
            mode: TcpMode::Client,
        }
    }

    pub fn new_server(config: TcpConfig, bind_address: String) -> Self {
        Self {
            config,
            connection: None,
            status: TransportStatus::Disconnected,
            statistics: TransportStatistics::default(),
            protocol: NetworkProtocol::new(),
            mode: TcpMode::Server { bind_address },
        }
    }
}

#[async_trait]
impl DataTransport for TcpTransport {
    async fn connect(&mut self) -> Result<()> {
        self.status = TransportStatus::Connecting;

        match &self.mode {
            TcpMode::Client => {
                let stream = tokio::time::timeout(
                    self.config.connect_timeout,
                    TcpStream::connect(format!("{}:{}", self.config.host, self.config.port))
                ).await??;

                // 设置TCP选项
                if self.config.keep_alive {
                    stream.set_keepalive(Some(Duration::from_secs(60)))?;
                }
                if self.config.no_delay {
                    stream.set_nodelay(true)?;
                }

                self.connection = Some(Arc::new(Mutex::new(stream)));
                self.status = TransportStatus::Connected;

                tracing::info!("TCP client connected to {}:{}", self.config.host, self.config.port);
            }
            TcpMode::Server { bind_address } => {
                let listener = TcpListener::bind(format!("{}:{}", bind_address, self.config.port)).await?;
                tracing::info!("TCP server listening on {}:{}", bind_address, self.config.port);

                // 等待客户端连接
                let (stream, addr) = listener.accept().await?;
                tracing::info!("TCP server accepted connection from {}", addr);

                self.connection = Some(Arc::new(Mutex::new(stream)));
                self.status = TransportStatus::Connected;
            }
        }

        Ok(())
    }

    async fn disconnect(&mut self) -> Result<()> {
        if let Some(connection) = &self.connection {
            let mut stream = connection.lock().await;
            stream.shutdown().await?;
        }

        self.connection = None;
        self.status = TransportStatus::Disconnected;

        tracing::info!("TCP connection disconnected");
        Ok(())
    }

    async fn send(&mut self, data: &NormalizedData) -> Result<TransmissionResult> {
        let start_time = Instant::now();

        if self.connection.is_none() {
            return Err(anyhow!("TCP connection not established"));
        }

        self.status = TransportStatus::Transmitting;

        // 编码数据
        let packet = self.protocol.encode_data(data)?;
        let message_id = uuid::Uuid::new_v4().to_string();

        // 发送数据
        let connection = self.connection.as_ref().unwrap();
        let mut stream = connection.lock().await;

        let bytes_sent = tokio::time::timeout(
            self.config.write_timeout,
            self.send_packet(&mut *stream, &packet)
        ).await??;

        // 等待确认响应
        let response = tokio::time::timeout(
            self.config.read_timeout,
            self.receive_response(&mut *stream)
        ).await??;

        let response_time = start_time.elapsed().as_millis() as u64;

        self.status = TransportStatus::Connected;
        self.statistics.total_sent += 1;

        if response.success {
            self.statistics.success_count += 1;
        } else {
            self.statistics.error_count += 1;
        }

        self.statistics.bytes_sent += bytes_sent as u64;

        Ok(TransmissionResult {
            success: response.success,
            message_id,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            bytes_sent,
            response_time_ms: response_time,
            error: if response.success { None } else { Some(TransportError::ProtocolError(response.error_message)) },
        })
    }

    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<Vec<TransmissionResult>> {
        let mut results = Vec::new();

        for item in data {
            match self.send(item).await {
                Ok(result) => results.push(result),
                Err(e) => {
                    results.push(TransmissionResult {
                        success: false,
                        message_id: uuid::Uuid::new_v4().to_string(),
                        timestamp: chrono::Utc::now().timestamp_millis() as u64,
                        bytes_sent: 0,
                        response_time_ms: 0,
                        error: Some(TransportError::SendFailed(e.to_string())),
                    });
                }
            }
        }

        Ok(results)
    }

    async fn get_status(&self) -> TransportStatus {
        self.status.clone()
    }

    fn get_transport_type(&self) -> TransportType {
        match &self.mode {
            TcpMode::Client => TransportType::TcpClient {
                host: self.config.host.clone(),
                port: self.config.port,
            },
            TcpMode::Server { bind_address } => TransportType::TcpServer {
                bind_address: bind_address.clone(),
                port: self.config.port,
            },
        }
    }

    fn get_statistics(&self) -> TransportStatistics {
        self.statistics.clone()
    }
}

impl TcpTransport {
    async fn send_packet(&self, stream: &mut TcpStream, packet: &[u8]) -> Result<usize> {
        stream.write_all(packet).await?;
        stream.flush().await?;
        Ok(packet.len())
    }

    async fn receive_response(&self, stream: &mut TcpStream) -> Result<ResponsePacket> {
        // 读取响应包头
        let mut header = [0u8; 8];
        stream.read_exact(&mut header).await?;

        // 解析包头
        let packet_length = u32::from_be_bytes([header[4], header[5], header[6], header[7]]) as usize;

        // 读取响应内容
        let mut content = vec![0u8; packet_length];
        stream.read_exact(&mut content).await?;

        // 解析响应
        self.protocol.decode_response(&content)
    }
}

#[derive(Debug)]
struct ResponsePacket {
    success: bool,
    error_message: String,
}
```

### 5.2 UDP传输器

```rust
// transport/src/udp.rs
use async_trait::async_trait;
use anyhow::{Result, anyhow};
use tokio::net::UdpSocket;
use std::time::Instant;
use std::net::SocketAddr;

pub struct UdpTransport {
    config: UdpConfig,
    socket: Option<UdpSocket>,
    target_addr: SocketAddr,
    status: TransportStatus,
    statistics: TransportStatistics,
    protocol: NetworkProtocol,
}

#[derive(Debug, Clone)]
pub struct UdpConfig {
    pub host: String,
    pub port: u16,
    pub bind_port: Option<u16>,
    pub timeout: Duration,
}

impl UdpTransport {
    pub fn new(config: UdpConfig) -> Result<Self> {
        let target_addr = format!("{}:{}", config.host, config.port).parse()?;

        Ok(Self {
            config,
            socket: None,
            target_addr,
            status: TransportStatus::Disconnected,
            statistics: TransportStatistics::default(),
            protocol: NetworkProtocol::new(),
        })
    }
}

#[async_trait]
impl DataTransport for UdpTransport {
    async fn connect(&mut self) -> Result<()> {
        self.status = TransportStatus::Connecting;

        let bind_addr = if let Some(bind_port) = self.config.bind_port {
            format!("0.0.0.0:{}", bind_port)
        } else {
            "0.0.0.0:0".to_string()
        };

        let socket = UdpSocket::bind(bind_addr).await?;
        self.socket = Some(socket);
        self.status = TransportStatus::Connected;

        tracing::info!("UDP socket connected to {}", self.target_addr);
        Ok(())
    }

    async fn disconnect(&mut self) -> Result<()> {
        self.socket = None;
        self.status = TransportStatus::Disconnected;

        tracing::info!("UDP socket disconnected");
        Ok(())
    }

    async fn send(&mut self, data: &NormalizedData) -> Result<TransmissionResult> {
        let start_time = Instant::now();

        if self.socket.is_none() {
            return Err(anyhow!("UDP socket not connected"));
        }

        self.status = TransportStatus::Transmitting;

        // 编码数据
        let packet = self.protocol.encode_data(data)?;
        let message_id = uuid::Uuid::new_v4().to_string();

        // 发送数据
        let socket = self.socket.as_ref().unwrap();
        let bytes_sent = socket.send_to(&packet, self.target_addr).await?;

        let response_time = start_time.elapsed().as_millis() as u64;

        self.status = TransportStatus::Connected;
        self.statistics.total_sent += 1;
        self.statistics.success_count += 1;
        self.statistics.bytes_sent += bytes_sent as u64;

        Ok(TransmissionResult {
            success: true,
            message_id,
            timestamp: chrono::Utc::now().timestamp_millis() as u64,
            bytes_sent,
            response_time_ms: response_time,
            error: None,
        })
    }

    async fn send_batch(&mut self, data: &[NormalizedData]) -> Result<Vec<TransmissionResult>> {
        let mut results = Vec::new();

        for item in data {
            match self.send(item).await {
                Ok(result) => results.push(result),
                Err(e) => {
                    results.push(TransmissionResult {
                        success: false,
                        message_id: uuid::Uuid::new_v4().to_string(),
                        timestamp: chrono::Utc::now().timestamp_millis() as u64,
                        bytes_sent: 0,
                        response_time_ms: 0,
                        error: Some(TransportError::SendFailed(e.to_string())),
                    });
                }
            }
        }

        Ok(results)
    }

    async fn get_status(&self) -> TransportStatus {
        self.status.clone()
    }

    fn get_transport_type(&self) -> TransportType {
        TransportType::Udp {
            host: self.config.host.clone(),
            port: self.config.port,
        }
    }

    fn get_statistics(&self) -> TransportStatistics {
        self.statistics.clone()
    }
}
```

### 5.3 网络协议处理

```rust
// transport/src/network_protocol.rs
use anyhow::{Result, anyhow};
use types::NormalizedData;
use crc32fast;

pub struct NetworkProtocol {
    version: u8,
}

impl NetworkProtocol {
    pub fn new() -> Self {
        Self { version: 1 }
    }

    /// 编码数据为网络包格式
    pub fn encode_data(&self, data: &NormalizedData) -> Result<Vec<u8>> {
        // 序列化数据
        let json_data = serde_json::to_vec(data)?;

        // 构建网络包
        let mut packet = Vec::new();

        // 包头 (4字节): "HCS\0"
        packet.extend_from_slice(b"HCS\0");

        // 版本 (1字节)
        packet.push(self.version);

        // 数据长度 (4字节，大端序)
        let data_length = json_data.len() as u32;
        packet.extend_from_slice(&data_length.to_be_bytes());

        // 数据类型 (1字节)
        let data_type = if data.is_transaction() { 0x01 } else { 0x02 };
        packet.push(data_type);

        // 数据内容
        packet.extend_from_slice(&json_data);

        // 校验码 (4字节，CRC32)
        let checksum = crc32fast::hash(&packet[5..]); // 不包含包头的校验
        packet.extend_from_slice(&checksum.to_be_bytes());

        Ok(packet)
    }

    /// 解码网络包数据
    pub fn decode_packet(&self, packet: &[u8]) -> Result<NormalizedData> {
        if packet.len() < 14 { // 最小包长度：包头(4) + 版本(1) + 长度(4) + 类型(1) + 校验(4)
            return Err(anyhow!("Packet too short"));
        }

        // 检查包头
        if &packet[0..4] != b"HCS\0" {
            return Err(anyhow!("Invalid packet header"));
        }

        // 检查版本
        if packet[4] != self.version {
            return Err(anyhow!("Unsupported protocol version: {}", packet[4]));
        }

        // 提取数据长度
        let data_length = u32::from_be_bytes([packet[5], packet[6], packet[7], packet[8]]) as usize;

        if packet.len() != 14 + data_length {
            return Err(anyhow!("Packet length mismatch"));
        }

        // 提取数据类型
        let _data_type = packet[9];

        // 提取数据内容
        let data_start = 10;
        let data_end = data_start + data_length;
        let json_data = &packet[data_start..data_end];

        // 验证校验码
        let expected_checksum = u32::from_be_bytes([
            packet[data_end], packet[data_end + 1],
            packet[data_end + 2], packet[data_end + 3]
        ]);
        let actual_checksum = crc32fast::hash(&packet[5..data_end]);

        if expected_checksum != actual_checksum {
            return Err(anyhow!("Checksum verification failed"));
        }

        // 反序列化数据
        let normalized_data: NormalizedData = serde_json::from_slice(json_data)?;
        Ok(normalized_data)
    }

    /// 解码响应包
    pub fn decode_response(&self, content: &[u8]) -> Result<ResponsePacket> {
        if content.is_empty() {
            return Ok(ResponsePacket {
                success: true,
                error_message: String::new(),
            });
        }

        // 简单的响应格式：第一个字节表示成功/失败，后续为错误消息
        let success = content[0] == 0x01;
        let error_message = if success {
            String::new()
        } else {
            String::from_utf8_lossy(&content[1..]).to_string()
        };

        Ok(ResponsePacket {
            success,
            error_message,
        })
    }

    /// 创建确认响应
    pub fn create_ack_response(&self, success: bool, message: &str) -> Vec<u8> {
        let mut response = Vec::new();

        if success {
            response.push(0x01);
        } else {
            response.push(0x00);
            response.extend_from_slice(message.as_bytes());
        }

        response
    }
}
```

## 6. 传输队列管理

### 6.1 传输队列实现

```rust
// transport/src/transmission_queue.rs
use anyhow::{Result, anyhow};
use std::collections::VecDeque;
use types::NormalizedData;

pub struct TransmissionQueue {
    queue: VecDeque<TransmissionItem>,
    max_size: usize,
    total_enqueued: u64,
    total_dequeued: u64,
}

#[derive(Debug, Clone)]
pub struct TransmissionItem {
    pub id: String,
    pub transport_name: String,
    pub data: NormalizedData,
    pub created_at: u64,
    pub retry_count: u32,
    pub max_retries: u32,
    pub priority: Priority,
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum Priority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

impl TransmissionQueue {
    pub fn new(max_size: usize) -> Self {
        Self {
            queue: VecDeque::new(),
            max_size,
            total_enqueued: 0,
            total_dequeued: 0,
        }
    }

    /// 入队数据
    pub async fn enqueue(&mut self, item: TransmissionItem) -> Result<()> {
        if self.queue.len() >= self.max_size {
            return Err(anyhow!("Transmission queue is full"));
        }

        // 根据优先级插入
        let insert_pos = self.find_insert_position(&item.priority);
        self.queue.insert(insert_pos, item);
        self.total_enqueued += 1;

        Ok(())
    }

    /// 出队数据
    pub async fn dequeue(&mut self) -> Option<TransmissionItem> {
        if let Some(item) = self.queue.pop_front() {
            self.total_dequeued += 1;
            Some(item)
        } else {
            None
        }
    }

    /// 获取队列大小
    pub fn size(&self) -> usize {
        self.queue.len()
    }

    /// 获取队列统计信息
    pub fn get_statistics(&self) -> QueueStatistics {
        QueueStatistics {
            current_size: self.queue.len(),
            max_size: self.max_size,
            total_enqueued: self.total_enqueued,
            total_dequeued: self.total_dequeued,
            pending_count: self.queue.len(),
        }
    }

    /// 清空队列
    pub fn clear(&mut self) {
        self.queue.clear();
    }

    /// 根据优先级查找插入位置
    fn find_insert_position(&self, priority: &Priority) -> usize {
        for (index, item) in self.queue.iter().enumerate() {
            if item.priority < *priority {
                return index;
            }
        }
        self.queue.len()
    }

    /// 移除超时的项目
    pub fn remove_expired_items(&mut self, timeout_ms: u64) -> Vec<TransmissionItem> {
        let current_time = chrono::Utc::now().timestamp_millis() as u64;
        let mut expired_items = Vec::new();

        self.queue.retain(|item| {
            if current_time - item.created_at > timeout_ms {
                expired_items.push(item.clone());
                false
            } else {
                true
            }
        });

        expired_items
    }
}

#[derive(Debug, Clone)]
pub struct QueueStatistics {
    pub current_size: usize,
    pub max_size: usize,
    pub total_enqueued: u64,
    pub total_dequeued: u64,
    pub pending_count: usize,
}
```

### 6.2 重试管理器

```rust
// transport/src/retry_manager.rs
use std::collections::HashMap;
use std::time::{Duration, Instant};

pub struct RetryManager {
    max_retry_attempts: u32,
    retry_intervals: Vec<Duration>,
    retry_records: HashMap<String, RetryRecord>,
}

#[derive(Debug, Clone)]
struct RetryRecord {
    attempts: u32,
    last_attempt: Instant,
    next_retry_time: Instant,
}

impl RetryManager {
    pub fn new(max_retry_attempts: u32) -> Self {
        // 指数退避重试间隔：1s, 2s, 4s, 8s, 16s
        let retry_intervals = vec![
            Duration::from_secs(1),
            Duration::from_secs(2),
            Duration::from_secs(4),
            Duration::from_secs(8),
            Duration::from_secs(16),
        ];

        Self {
            max_retry_attempts,
            retry_intervals,
            retry_records: HashMap::new(),
        }
    }

    /// 判断是否应该重试
    pub fn should_retry(&mut self, item: &TransmissionItem) -> bool {
        if item.retry_count >= self.max_retry_attempts {
            self.retry_records.remove(&item.id);
            return false;
        }

        let now = Instant::now();

        if let Some(record) = self.retry_records.get(&item.id) {
            if now < record.next_retry_time {
                return false; // 还未到重试时间
            }
        }

        // 更新重试记录
        let interval_index = (item.retry_count as usize).min(self.retry_intervals.len() - 1);
        let retry_interval = self.retry_intervals[interval_index];

        let record = RetryRecord {
            attempts: item.retry_count + 1,
            last_attempt: now,
            next_retry_time: now + retry_interval,
        };

        self.retry_records.insert(item.id.clone(), record);
        true
    }

    /// 清理过期的重试记录
    pub fn cleanup_expired_records(&mut self, max_age: Duration) {
        let now = Instant::now();
        self.retry_records.retain(|_, record| {
            now.duration_since(record.last_attempt) < max_age
        });
    }

    /// 获取重试统计信息
    pub fn get_statistics(&self) -> RetryStatistics {
        let total_retrying = self.retry_records.len();
        let mut attempts_distribution = HashMap::new();

        for record in self.retry_records.values() {
            *attempts_distribution.entry(record.attempts).or_insert(0) += 1;
        }

        RetryStatistics {
            total_retrying,
            attempts_distribution,
        }
    }
}

#[derive(Debug, Clone)]
pub struct RetryStatistics {
    pub total_retrying: usize,
    pub attempts_distribution: HashMap<u32, u32>,
}
```

## 7. 连接池管理

### 7.1 连接池实现

```rust
// transport/src/connection_pool.rs
use anyhow::{Result, anyhow};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, Semaphore};
use tokio::time::{Duration, Instant};

pub struct ConnectionPool<T> {
    connections: Arc<Mutex<HashMap<String, PooledConnection<T>>>>,
    semaphore: Arc<Semaphore>,
    max_connections: usize,
    connection_timeout: Duration,
    idle_timeout: Duration,
}

#[derive(Debug)]
struct PooledConnection<T> {
    connection: T,
    created_at: Instant,
    last_used: Instant,
    in_use: bool,
}

impl<T> ConnectionPool<T>
where
    T: Send + Sync + 'static,
{
    pub fn new(max_connections: usize, connection_timeout: Duration, idle_timeout: Duration) -> Self {
        Self {
            connections: Arc::new(Mutex::new(HashMap::new())),
            semaphore: Arc::new(Semaphore::new(max_connections)),
            max_connections,
            connection_timeout,
            idle_timeout,
        }
    }

    /// 获取连接
    pub async fn get_connection(&self, key: &str) -> Result<Arc<Mutex<T>>> {
        let _permit = self.semaphore.acquire().await.map_err(|_| anyhow!("Failed to acquire connection permit"))?;

        let mut connections = self.connections.lock().await;

        if let Some(pooled_conn) = connections.get_mut(key) {
            if !pooled_conn.in_use && !self.is_connection_expired(&pooled_conn) {
                pooled_conn.in_use = true;
                pooled_conn.last_used = Instant::now();
                return Ok(Arc::new(Mutex::new(pooled_conn.connection)));
            }
        }

        Err(anyhow!("No available connection for key: {}", key))
    }

    /// 添加连接到池中
    pub async fn add_connection(&self, key: String, connection: T) -> Result<()> {
        let mut connections = self.connections.lock().await;

        if connections.len() >= self.max_connections {
            return Err(anyhow!("Connection pool is full"));
        }

        let pooled_conn = PooledConnection {
            connection,
            created_at: Instant::now(),
            last_used: Instant::now(),
            in_use: false,
        };

        connections.insert(key, pooled_conn);
        Ok(())
    }

    /// 释放连接
    pub async fn release_connection(&self, key: &str) -> Result<()> {
        let mut connections = self.connections.lock().await;

        if let Some(pooled_conn) = connections.get_mut(key) {
            pooled_conn.in_use = false;
            pooled_conn.last_used = Instant::now();
        }

        Ok(())
    }

    /// 清理过期连接
    pub async fn cleanup_expired_connections(&self) {
        let mut connections = self.connections.lock().await;
        let now = Instant::now();

        connections.retain(|_, pooled_conn| {
            !self.is_connection_expired(pooled_conn) &&
            (pooled_conn.in_use || now.duration_since(pooled_conn.last_used) < self.idle_timeout)
        });
    }

    /// 检查连接是否过期
    fn is_connection_expired(&self, connection: &PooledConnection<T>) -> bool {
        let now = Instant::now();
        now.duration_since(connection.created_at) > self.connection_timeout
    }

    /// 获取连接池统计信息
    pub async fn get_statistics(&self) -> PoolStatistics {
        let connections = self.connections.lock().await;
        let total_connections = connections.len();
        let in_use_connections = connections.values().filter(|conn| conn.in_use).count();
        let available_connections = total_connections - in_use_connections;

        PoolStatistics {
            total_connections,
            in_use_connections,
            available_connections,
            max_connections: self.max_connections,
        }
    }
}

#[derive(Debug, Clone)]
pub struct PoolStatistics {
    pub total_connections: usize,
    pub in_use_connections: usize,
    pub available_connections: usize,
    pub max_connections: usize,
}
```

## 8. 配置管理

### 8.1 传输配置

```yaml
# config/transport.yaml
transport:
  # 全局配置
  global:
    max_concurrent_transmissions: 100
    transmission_timeout_ms: 30000
    retry_max_attempts: 3
    retry_base_interval_ms: 1000
    queue_max_size: 10000

  # 串口传输配置
  serial:
    enabled: true
    devices:
      - name: "device1"
        port: "/dev/ttyUSB0"
        baud_rate: 115200
        data_bits: 8
        stop_bits: 1
        parity: "none"  # none | odd | even
        flow_control: "none"  # none | hardware | software
        timeout_ms: 5000
      - name: "device2"
        port: "/dev/ttyUSB1"
        baud_rate: 9600
        data_bits: 8
        stop_bits: 1
        parity: "even"
        timeout_ms: 3000

  # TCP传输配置
  tcp:
    enabled: true
    clients:
      - name: "server1"
        host: "*************"
        port: 8080
        connect_timeout_ms: 30000
        read_timeout_ms: 10000
        write_timeout_ms: 10000
        keep_alive: true
        no_delay: true
        max_connections: 5
      - name: "server2"
        host: "*************"
        port: 8081
        connect_timeout_ms: 30000
        max_connections: 3

    servers:
      - name: "listener1"
        bind_address: "0.0.0.0"
        port: 9090
        max_connections: 100
        keep_alive: true

  # UDP传输配置
  udp:
    enabled: true
    clients:
      - name: "udp_server1"
        host: "*************"
        port: 8888
        bind_port: 9999
        timeout_ms: 5000
      - name: "udp_server2"
        host: "*************"
        port: 8889
        timeout_ms: 3000

  # 传输策略配置
  strategy:
    default_transport: "tcp"
    failover_enabled: true
    failover_order: ["tcp", "udp", "serial"]
    load_balancing: "round_robin"  # round_robin | least_connections | random

  # 监控配置
  monitoring:
    metrics_enabled: true
    health_check_interval_ms: 30000
    statistics_report_interval_ms: 60000
    alert_thresholds:
      error_rate_percent: 5.0
      response_time_ms: 10000
      queue_size_percent: 80.0
```

### 8.2 配置加载器

```rust
// transport/src/config.rs
use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::time::Duration;
use std::collections::HashMap;

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct TransportConfig {
    pub global: GlobalConfig,
    pub serial: SerialConfig,
    pub tcp: TcpConfig,
    pub udp: UdpConfig,
    pub strategy: StrategyConfig,
    pub monitoring: MonitoringConfig,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct GlobalConfig {
    pub max_concurrent_transmissions: usize,
    pub transmission_timeout_ms: u64,
    pub retry_max_attempts: u32,
    pub retry_base_interval_ms: u64,
    pub queue_max_size: usize,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SerialConfig {
    pub enabled: bool,
    pub devices: Vec<SerialDeviceConfig>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SerialDeviceConfig {
    pub name: String,
    pub port: String,
    pub baud_rate: u32,
    pub data_bits: u8,
    pub stop_bits: u8,
    pub parity: String,
    pub flow_control: String,
    pub timeout_ms: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct TcpConfig {
    pub enabled: bool,
    pub clients: Vec<TcpClientConfig>,
    pub servers: Vec<TcpServerConfig>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct TcpClientConfig {
    pub name: String,
    pub host: String,
    pub port: u16,
    pub connect_timeout_ms: u64,
    pub read_timeout_ms: u64,
    pub write_timeout_ms: u64,
    pub keep_alive: bool,
    pub no_delay: bool,
    pub max_connections: usize,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct TcpServerConfig {
    pub name: String,
    pub bind_address: String,
    pub port: u16,
    pub max_connections: usize,
    pub keep_alive: bool,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct UdpConfig {
    pub enabled: bool,
    pub clients: Vec<UdpClientConfig>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct UdpClientConfig {
    pub name: String,
    pub host: String,
    pub port: u16,
    pub bind_port: Option<u16>,
    pub timeout_ms: u64,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct StrategyConfig {
    pub default_transport: String,
    pub failover_enabled: bool,
    pub failover_order: Vec<String>,
    pub load_balancing: String,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct MonitoringConfig {
    pub metrics_enabled: bool,
    pub health_check_interval_ms: u64,
    pub statistics_report_interval_ms: u64,
    pub alert_thresholds: AlertThresholds,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct AlertThresholds {
    pub error_rate_percent: f64,
    pub response_time_ms: u64,
    pub queue_size_percent: f64,
}

impl TransportConfig {
    /// 从文件加载配置
    pub fn load_from_file(path: &str) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: TransportConfig = serde_yaml::from_str(&content)?;
        Ok(config)
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证全局配置
        if self.global.max_concurrent_transmissions == 0 {
            return Err(anyhow::anyhow!("max_concurrent_transmissions must be greater than 0"));
        }

        if self.global.queue_max_size == 0 {
            return Err(anyhow::anyhow!("queue_max_size must be greater than 0"));
        }

        // 验证串口配置
        if self.serial.enabled {
            for device in &self.serial.devices {
                if device.name.is_empty() {
                    return Err(anyhow::anyhow!("Serial device name cannot be empty"));
                }
                if device.port.is_empty() {
                    return Err(anyhow::anyhow!("Serial port cannot be empty"));
                }
                if device.baud_rate == 0 {
                    return Err(anyhow::anyhow!("Baud rate must be greater than 0"));
                }
            }
        }

        // 验证TCP配置
        if self.tcp.enabled {
            for client in &self.tcp.clients {
                if client.name.is_empty() {
                    return Err(anyhow::anyhow!("TCP client name cannot be empty"));
                }
                if client.host.is_empty() {
                    return Err(anyhow::anyhow!("TCP host cannot be empty"));
                }
                if client.port == 0 {
                    return Err(anyhow::anyhow!("TCP port must be greater than 0"));
                }
            }
        }

        Ok(())
    }

    /// 获取传输超时时间
    pub fn get_transmission_timeout(&self) -> Duration {
        Duration::from_millis(self.global.transmission_timeout_ms)
    }

    /// 获取重试间隔
    pub fn get_retry_interval(&self) -> Duration {
        Duration::from_millis(self.global.retry_base_interval_ms)
    }
}
```

## 9. 监控和统计

### 9.1 传输指标收集器

```rust
// transport/src/metrics.rs
use std::sync::atomic::{AtomicU64, AtomicF64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::sync::Mutex;

pub struct TransportMetrics {
    // 基础计数器
    total_sent: AtomicU64,
    success_count: AtomicU64,
    error_count: AtomicU64,
    bytes_sent: AtomicU64,

    // 时间统计
    total_response_time: AtomicU64,
    min_response_time: AtomicU64,
    max_response_time: AtomicU64,

    // 分类统计
    transport_stats: Arc<Mutex<HashMap<String, TransportTypeStats>>>,
    error_stats: Arc<Mutex<HashMap<String, u64>>>,

    // 实时统计
    start_time: Instant,
    last_reset: Arc<Mutex<Instant>>,
}

#[derive(Debug, Clone, Default)]
pub struct TransportTypeStats {
    pub sent: u64,
    pub success: u64,
    pub errors: u64,
    pub bytes: u64,
    pub avg_response_time: f64,
}

impl TransportMetrics {
    pub fn new() -> Self {
        Self {
            total_sent: AtomicU64::new(0),
            success_count: AtomicU64::new(0),
            error_count: AtomicU64::new(0),
            bytes_sent: AtomicU64::new(0),
            total_response_time: AtomicU64::new(0),
            min_response_time: AtomicU64::new(u64::MAX),
            max_response_time: AtomicU64::new(0),
            transport_stats: Arc::new(Mutex::new(HashMap::new())),
            error_stats: Arc::new(Mutex::new(HashMap::new())),
            start_time: Instant::now(),
            last_reset: Arc::new(Mutex::new(Instant::now())),
        }
    }

    /// 记录成功传输
    pub fn record_success(&self, response_time_ms: u64, bytes: usize) {
        self.total_sent.fetch_add(1, Ordering::Relaxed);
        self.success_count.fetch_add(1, Ordering::Relaxed);
        self.bytes_sent.fetch_add(bytes as u64, Ordering::Relaxed);
        self.total_response_time.fetch_add(response_time_ms, Ordering::Relaxed);

        // 更新最小响应时间
        let mut current_min = self.min_response_time.load(Ordering::Relaxed);
        while response_time_ms < current_min {
            match self.min_response_time.compare_exchange_weak(
                current_min,
                response_time_ms,
                Ordering::Relaxed,
                Ordering::Relaxed
            ) {
                Ok(_) => break,
                Err(x) => current_min = x,
            }
        }

        // 更新最大响应时间
        let mut current_max = self.max_response_time.load(Ordering::Relaxed);
        while response_time_ms > current_max {
            match self.max_response_time.compare_exchange_weak(
                current_max,
                response_time_ms,
                Ordering::Relaxed,
                Ordering::Relaxed
            ) {
                Ok(_) => break,
                Err(x) => current_max = x,
            }
        }
    }

    /// 记录传输错误
    pub fn record_error(&self) {
        self.total_sent.fetch_add(1, Ordering::Relaxed);
        self.error_count.fetch_add(1, Ordering::Relaxed);
    }

    /// 记录特定传输类型的统计
    pub async fn record_transport_stats(&self, transport_type: &str, success: bool, response_time_ms: u64, bytes: usize) {
        let mut stats = self.transport_stats.lock().await;
        let entry = stats.entry(transport_type.to_string()).or_default();

        entry.sent += 1;
        entry.bytes += bytes as u64;

        if success {
            entry.success += 1;
        } else {
            entry.errors += 1;
        }

        // 更新平均响应时间
        let total_requests = entry.success + entry.errors;
        if total_requests > 0 {
            entry.avg_response_time = (entry.avg_response_time * (total_requests - 1) as f64 + response_time_ms as f64) / total_requests as f64;
        }
    }

    /// 记录错误类型统计
    pub async fn record_error_type(&self, error_type: &str) {
        let mut error_stats = self.error_stats.lock().await;
        *error_stats.entry(error_type.to_string()).or_insert(0) += 1;
    }

    /// 获取总体统计信息
    pub async fn get_overall_statistics(&self) -> OverallStatistics {
        let total = self.total_sent.load(Ordering::Relaxed);
        let success = self.success_count.load(Ordering::Relaxed);
        let errors = self.error_count.load(Ordering::Relaxed);
        let bytes = self.bytes_sent.load(Ordering::Relaxed);
        let total_time = self.total_response_time.load(Ordering::Relaxed);

        let success_rate = if total > 0 { (success as f64 / total as f64) * 100.0 } else { 0.0 };
        let avg_response_time = if success > 0 { total_time as f64 / success as f64 } else { 0.0 };
        let uptime = self.start_time.elapsed();
        let throughput = if uptime.as_secs() > 0 { total as f64 / uptime.as_secs() as f64 } else { 0.0 };

        OverallStatistics {
            total_sent: total,
            success_count: success,
            error_count: errors,
            success_rate_percent: success_rate,
            bytes_sent: bytes,
            avg_response_time_ms: avg_response_time,
            min_response_time_ms: self.min_response_time.load(Ordering::Relaxed),
            max_response_time_ms: self.max_response_time.load(Ordering::Relaxed),
            throughput_per_second: throughput,
            uptime_seconds: uptime.as_secs(),
        }
    }

    /// 获取传输类型统计
    pub async fn get_transport_statistics(&self) -> HashMap<String, TransportTypeStats> {
        self.transport_stats.lock().await.clone()
    }

    /// 获取错误类型统计
    pub async fn get_error_statistics(&self) -> HashMap<String, u64> {
        self.error_stats.lock().await.clone()
    }

    /// 重置统计信息
    pub async fn reset_statistics(&self) {
        self.total_sent.store(0, Ordering::Relaxed);
        self.success_count.store(0, Ordering::Relaxed);
        self.error_count.store(0, Ordering::Relaxed);
        self.bytes_sent.store(0, Ordering::Relaxed);
        self.total_response_time.store(0, Ordering::Relaxed);
        self.min_response_time.store(u64::MAX, Ordering::Relaxed);
        self.max_response_time.store(0, Ordering::Relaxed);

        self.transport_stats.lock().await.clear();
        self.error_stats.lock().await.clear();

        *self.last_reset.lock().await = Instant::now();
    }
}

#[derive(Debug, Clone)]
pub struct OverallStatistics {
    pub total_sent: u64,
    pub success_count: u64,
    pub error_count: u64,
    pub success_rate_percent: f64,
    pub bytes_sent: u64,
    pub avg_response_time_ms: f64,
    pub min_response_time_ms: u64,
    pub max_response_time_ms: u64,
    pub throughput_per_second: f64,
    pub uptime_seconds: u64,
}
```

### 9.2 健康检查器

```rust
// transport/src/health_checker.rs
use anyhow::Result;
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::Mutex;
use tokio::time::{Duration, Instant, interval};

pub struct HealthChecker {
    transports: Arc<Mutex<HashMap<String, Arc<Mutex<dyn DataTransport>>>>>,
    health_status: Arc<Mutex<HashMap<String, HealthStatus>>>,
    check_interval: Duration,
    timeout: Duration,
}

#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub is_healthy: bool,
    pub last_check: Instant,
    pub consecutive_failures: u32,
    pub last_error: Option<String>,
    pub response_time_ms: u64,
}

impl HealthChecker {
    pub fn new(check_interval: Duration, timeout: Duration) -> Self {
        Self {
            transports: Arc::new(Mutex::new(HashMap::new())),
            health_status: Arc::new(Mutex::new(HashMap::new())),
            check_interval,
            timeout,
        }
    }

    /// 注册传输器进行健康检查
    pub async fn register_transport(&self, name: String, transport: Arc<Mutex<dyn DataTransport>>) {
        let mut transports = self.transports.lock().await;
        transports.insert(name.clone(), transport);

        let mut health_status = self.health_status.lock().await;
        health_status.insert(name, HealthStatus {
            is_healthy: true,
            last_check: Instant::now(),
            consecutive_failures: 0,
            last_error: None,
            response_time_ms: 0,
        });
    }

    /// 启动健康检查循环
    pub async fn start_health_check_loop(&self) -> Result<()> {
        let transports = Arc::clone(&self.transports);
        let health_status = Arc::clone(&self.health_status);
        let check_interval = self.check_interval;
        let timeout = self.timeout;

        tokio::spawn(async move {
            let mut interval = interval(check_interval);

            loop {
                interval.tick().await;

                let transport_list = {
                    let transports_guard = transports.lock().await;
                    transports_guard.clone()
                };

                for (name, transport) in transport_list {
                    let start_time = Instant::now();
                    let check_result = tokio::time::timeout(
                        timeout,
                        Self::check_transport_health(&transport)
                    ).await;

                    let response_time = start_time.elapsed().as_millis() as u64;

                    let mut health_guard = health_status.lock().await;
                    if let Some(status) = health_guard.get_mut(&name) {
                        status.last_check = Instant::now();
                        status.response_time_ms = response_time;

                        match check_result {
                            Ok(Ok(_)) => {
                                status.is_healthy = true;
                                status.consecutive_failures = 0;
                                status.last_error = None;
                                tracing::debug!("Health check passed for transport: {}", name);
                            }
                            Ok(Err(e)) | Err(_) => {
                                status.is_healthy = false;
                                status.consecutive_failures += 1;
                                status.last_error = Some(format!("{:?}", check_result));
                                tracing::warn!("Health check failed for transport {}: {:?}", name, check_result);
                            }
                        }
                    }
                }
            }
        });

        Ok(())
    }

    /// 检查单个传输器的健康状态
    async fn check_transport_health(transport: &Arc<Mutex<dyn DataTransport>>) -> Result<()> {
        let transport_guard = transport.lock().await;
        let status = transport_guard.get_status().await;

        match status {
            TransportStatus::Connected | TransportStatus::Transmitting => Ok(()),
            TransportStatus::Disconnected => Err(anyhow::anyhow!("Transport is disconnected")),
            TransportStatus::Connecting => Err(anyhow::anyhow!("Transport is still connecting")),
            TransportStatus::Error(msg) => Err(anyhow::anyhow!("Transport error: {}", msg)),
        }
    }

    /// 获取所有传输器的健康状态
    pub async fn get_health_status(&self) -> HashMap<String, HealthStatus> {
        self.health_status.lock().await.clone()
    }

    /// 获取健康的传输器列表
    pub async fn get_healthy_transports(&self) -> Vec<String> {
        let health_status = self.health_status.lock().await;
        health_status.iter()
            .filter(|(_, status)| status.is_healthy)
            .map(|(name, _)| name.clone())
            .collect()
    }

    /// 获取不健康的传输器列表
    pub async fn get_unhealthy_transports(&self) -> Vec<String> {
        let health_status = self.health_status.lock().await;
        health_status.iter()
            .filter(|(_, status)| !status.is_healthy)
            .map(|(name, _)| name.clone())
            .collect()
    }
}
```

## 10. 性能优化

### 10.1 批量传输优化

```rust
// transport/src/batch_optimizer.rs
use anyhow::Result;
use types::NormalizedData;
use std::time::{Duration, Instant};
use tokio::sync::mpsc;

pub struct BatchOptimizer {
    batch_size: usize,
    batch_timeout: Duration,
    compression_enabled: bool,
}

impl BatchOptimizer {
    pub fn new(batch_size: usize, batch_timeout: Duration, compression_enabled: bool) -> Self {
        Self {
            batch_size,
            batch_timeout,
            compression_enabled,
        }
    }

    /// 优化数据批次
    pub async fn optimize_batch(&self, data: Vec<NormalizedData>) -> Result<Vec<OptimizedBatch>> {
        let mut batches = Vec::new();
        let mut current_batch = Vec::new();
        let mut current_size = 0;

        for item in data {
            let item_size = self.estimate_item_size(&item);

            if current_batch.len() >= self.batch_size ||
               (current_size + item_size > self.get_max_batch_size()) {
                if !current_batch.is_empty() {
                    batches.push(self.create_optimized_batch(current_batch).await?);
                    current_batch = Vec::new();
                    current_size = 0;
                }
            }

            current_batch.push(item);
            current_size += item_size;
        }

        if !current_batch.is_empty() {
            batches.push(self.create_optimized_batch(current_batch).await?);
        }

        Ok(batches)
    }

    async fn create_optimized_batch(&self, data: Vec<NormalizedData>) -> Result<OptimizedBatch> {
        let original_size = data.iter().map(|item| self.estimate_item_size(item)).sum();

        let serialized_data = serde_json::to_vec(&data)?;

        let final_data = if self.compression_enabled {
            self.compress_data(&serialized_data).await?
        } else {
            serialized_data
        };

        Ok(OptimizedBatch {
            data,
            serialized_data: final_data,
            original_size,
            compressed_size: final_data.len(),
            compression_ratio: original_size as f64 / final_data.len() as f64,
        })
    }

    async fn compress_data(&self, data: &[u8]) -> Result<Vec<u8>> {
        use flate2::write::GzEncoder;
        use flate2::Compression;
        use std::io::Write;

        let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
        encoder.write_all(data)?;
        Ok(encoder.finish()?)
    }

    fn estimate_item_size(&self, item: &NormalizedData) -> usize {
        // 简单估算，实际应用中可以更精确
        std::mem::size_of::<NormalizedData>() +
        item.id.len() +
        item.gantry_code.len() +
        item.plate_no.as_ref().map(|s| s.len()).unwrap_or(0) +
        item.image_bytes.as_ref().map(|b| b.len()).unwrap_or(0)
    }

    fn get_max_batch_size(&self) -> usize {
        1024 * 1024 // 1MB
    }
}

#[derive(Debug)]
pub struct OptimizedBatch {
    pub data: Vec<NormalizedData>,
    pub serialized_data: Vec<u8>,
    pub original_size: usize,
    pub compressed_size: usize,
    pub compression_ratio: f64,
}
```

## 11. 错误处理和恢复

### 11.1 错误处理策略

| 错误类型 | 处理策略 | 恢复机制 | 重试次数 |
|---------|---------|---------|---------|
| 连接超时 | 自动重连 | 指数退避重试 | 3次 |
| 网络中断 | 切换备用连接 | 故障转移 | 5次 |
| 数据格式错误 | 记录错误日志 | 跳过错误数据 | 0次 |
| 传输超时 | 重新发送 | 增加超时时间 | 3次 |
| 队列满 | 丢弃低优先级数据 | 扩展队列或清理 | 1次 |
| 协议错误 | 重新协商协议 | 降级到兼容协议 | 2次 |

### 11.2 故障转移机制

```rust
// transport/src/failover.rs
use anyhow::Result;
use std::collections::VecDeque;

pub struct FailoverManager {
    primary_transport: String,
    backup_transports: VecDeque<String>,
    current_transport: String,
    failover_threshold: u32,
    consecutive_failures: u32,
}

impl FailoverManager {
    pub fn new(primary: String, backups: Vec<String>, threshold: u32) -> Self {
        Self {
            current_transport: primary.clone(),
            primary_transport: primary,
            backup_transports: backups.into(),
            failover_threshold: threshold,
            consecutive_failures: 0,
        }
    }

    /// 记录传输失败
    pub fn record_failure(&mut self) -> Option<String> {
        self.consecutive_failures += 1;

        if self.consecutive_failures >= self.failover_threshold {
            self.failover()
        } else {
            None
        }
    }

    /// 记录传输成功
    pub fn record_success(&mut self) {
        self.consecutive_failures = 0;
    }

    /// 执行故障转移
    fn failover(&mut self) -> Option<String> {
        if let Some(next_transport) = self.backup_transports.pop_front() {
            // 将当前传输器放到备用队列末尾
            self.backup_transports.push_back(self.current_transport.clone());
            self.current_transport = next_transport.clone();
            self.consecutive_failures = 0;

            tracing::warn!("Failover to transport: {}", next_transport);
            Some(next_transport)
        } else {
            tracing::error!("No backup transports available for failover");
            None
        }
    }

    /// 获取当前传输器
    pub fn get_current_transport(&self) -> &str {
        &self.current_transport
    }

    /// 恢复到主传输器
    pub fn restore_primary(&mut self) -> bool {
        if self.current_transport != self.primary_transport {
            // 将当前传输器放回备用队列
            self.backup_transports.push_front(self.current_transport.clone());
            self.current_transport = self.primary_transport.clone();
            self.consecutive_failures = 0;

            tracing::info!("Restored to primary transport: {}", self.primary_transport);
            true
        } else {
            false
        }
    }
}
```

## 12. 总结

数据传输模块是数据采集系统的关键输出组件，负责将处理后的数据可靠地传输到外部系统。本设计文档详细描述了：

1. **多协议支持**: 支持串口（RS232/RS485）和网络（TCP/UDP）多种传输协议
2. **可靠传输**: 完善的重试机制、故障转移、连接管理等可靠性保障
3. **高性能设计**: 批量传输、连接池、异步处理等性能优化策略
4. **灵活配置**: 支持多种传输策略和参数配置
5. **全面监控**: 传输指标收集、健康检查、错误统计等监控能力
6. **协议扩展**: 支持自定义协议和传输方式的扩展

该设计确保了数据传输的高效性、可靠性和可扩展性，为整个数据采集系统提供了稳定的数据输出能力。通过模块化的架构设计，可以根据实际需求灵活选择和配置传输方式，满足不同场景下的数据传输需求。
