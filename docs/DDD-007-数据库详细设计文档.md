# DDD-007 数据库详细设计文档

**文档编号：** DDD-007  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 数据库设计团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md  
**上级设计：** DDD-001-系统总体架构详细设计文档.md

---

## 1. 数据库概述

### 1.1 设计目的
数据库详细设计文档定义了数据采集系统的数据存储结构、表设计、索引策略、数据关系和性能优化方案，确保数据的完整性、一致性和高效访问。

### 1.2 需求追溯
本模块对应PRD-001中的以下需求：
- **数据存储要求**: 支持≥1TB数据存储，数据丢失率≤0.01%
- **查询性能要求**: 复杂查询响应时间≤3秒
- **数据完整性**: 确保交易数据和摄像头数据的完整性
- **数据保留**: 支持7年数据保留期，3年在线查询
- **并发支持**: 支持≥100并发查询

### 1.3 数据库选型
- **主数据库**: PostgreSQL 15+
- **缓存数据库**: Redis 7+
- **时序数据库**: InfluxDB 2.0+ (用于监控指标)
- **搜索引擎**: Elasticsearch 8.0+ (用于全文搜索)

## 2. 数据库架构设计

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "应用层"
        APP1[HCS主应用]
        APP2[API服务]
        APP3[管理界面]
    end

    subgraph "数据访问层"
        DAL1[数据访问层]
        POOL[连接池]
        CACHE[查询缓存]
    end

    subgraph "数据库集群"
        subgraph "PostgreSQL主从集群"
            PG_MASTER[PostgreSQL主库<br/>读写操作]
            PG_SLAVE1[PostgreSQL从库1<br/>只读查询]
            PG_SLAVE2[PostgreSQL从库2<br/>只读查询]
        end
        
        subgraph "缓存层"
            REDIS_MASTER[Redis主节点]
            REDIS_SLAVE[Redis从节点]
        end
        
        subgraph "时序数据库"
            INFLUX[InfluxDB<br/>监控指标]
        end
        
        subgraph "搜索引擎"
            ES[Elasticsearch<br/>全文搜索]
        end
    end

    subgraph "存储层"
        STORAGE1[SSD存储<br/>热数据]
        STORAGE2[HDD存储<br/>冷数据]
        BACKUP[备份存储]
    end

    APP1 --> DAL1
    APP2 --> DAL1
    APP3 --> DAL1
    
    DAL1 --> POOL
    POOL --> CACHE
    
    CACHE --> PG_MASTER
    CACHE --> PG_SLAVE1
    CACHE --> PG_SLAVE2
    CACHE --> REDIS_MASTER
    
    PG_MASTER --> PG_SLAVE1
    PG_MASTER --> PG_SLAVE2
    REDIS_MASTER --> REDIS_SLAVE
    
    DAL1 --> INFLUX
    DAL1 --> ES
    
    PG_MASTER --> STORAGE1
    PG_SLAVE1 --> STORAGE1
    PG_SLAVE2 --> STORAGE1
    INFLUX --> STORAGE2
    ES --> STORAGE1
    
    PG_MASTER --> BACKUP
    REDIS_MASTER --> BACKUP
```

### 2.2 数据分布策略

```mermaid
graph LR
    subgraph "数据分类"
        HOT[热数据<br/>近3个月]
        WARM[温数据<br/>3个月-1年]
        COLD[冷数据<br/>1年以上]
    end

    subgraph "存储策略"
        SSD[SSD存储<br/>高性能]
        SATA[SATA存储<br/>平衡性能]
        ARCHIVE[归档存储<br/>低成本]
    end

    subgraph "访问模式"
        REALTIME[实时查询]
        BATCH[批量分析]
        BACKUP[备份恢复]
    end

    HOT --> SSD
    WARM --> SATA
    COLD --> ARCHIVE
    
    SSD --> REALTIME
    SATA --> BATCH
    ARCHIVE --> BACKUP
```

## 3. 数据模型设计

### 3.1 概念数据模型

```mermaid
erDiagram
    GANTRY ||--o{ TRANSACTION_DATA : generates
    GANTRY ||--o{ VEHICLE_DATA : captures
    GANTRY ||--o{ CAMERA : has
    
    CAMERA ||--o{ VEHICLE_DATA : captures
    
    TRANSACTION_DATA ||--|| VEHICLE_DATA : may_relate_to
    
    TRANSACTION_DATA {
        uuid id PK
        string gantry_code FK
        bigint timestamp
        string plate_no
        string vehicle_type
        string macid
        integer recievable_fee
        integer actual_fee
        timestamp created_at
    }
    
    VEHICLE_DATA {
        uuid id PK
        string gantry_code FK
        string camera_code FK
        bigint timestamp
        string plate_no
        string plate_type
        string plate_color
        string vehicle_type
        bytea image_data
        timestamp created_at
    }
    
    GANTRY {
        string code PK
        string name
        string location
        string direction
        boolean is_active
        timestamp created_at
    }
    
    CAMERA {
        string code PK
        string gantry_code FK
        string name
        string ip_address
        integer port
        string status
        timestamp created_at
    }
    
    SYSTEM_CONFIG {
        string key PK
        string value
        string description
        timestamp updated_at
    }
    
    AUDIT_LOG {
        uuid id PK
        string operation
        string table_name
        string record_id
        json old_values
        json new_values
        string user_id
        timestamp created_at
    }
```

### 3.2 逻辑数据模型

#### 3.2.1 核心业务表

**交易数据表 (transaction_data)**
```sql
CREATE TABLE transaction_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    gantry_code VARCHAR(50) NOT NULL,
    timestamp BIGINT NOT NULL,
    plate_no VARCHAR(20),
    vehicle_type VARCHAR(50),
    camera_code VARCHAR(50),
    plate_type VARCHAR(50),
    plate_color VARCHAR(50),
    vehicle_cat VARCHAR(50),
    vehicle_size VARCHAR(50),
    vehicle_object VARCHAR(100),
    macid VARCHAR(100),
    vehicle_fee_type VARCHAR(50),
    recievable_fee INTEGER,
    actual_fee INTEGER,
    pro_recievable_fee INTEGER,
    pro_actual_fee INTEGER,
    image_bytes BYTEA,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);
```

**摄像头数据表 (vehicle_data)**
```sql
CREATE TABLE vehicle_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    gantry_code VARCHAR(50) NOT NULL,
    camera_code VARCHAR(50) NOT NULL,
    timestamp BIGINT NOT NULL,
    plate_no VARCHAR(20),
    plate_type VARCHAR(50),
    plate_color VARCHAR(50),
    vehicle_type VARCHAR(50),
    vehicle_cat VARCHAR(50),
    vehicle_size VARCHAR(50),
    vehicle_object VARCHAR(100),
    image_bytes BYTEA,
    image_url VARCHAR(500),
    confidence_score DECIMAL(3,2),
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);
```

#### 3.2.2 配置管理表

**门架配置表 (gantries)**
```sql
CREATE TABLE gantries (
    code VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    location VARCHAR(200),
    direction VARCHAR(50),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_active BOOLEAN DEFAULT true,
    config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

**摄像头配置表 (cameras)**
```sql
CREATE TABLE cameras (
    code VARCHAR(50) PRIMARY KEY,
    gantry_code VARCHAR(50) NOT NULL REFERENCES gantries(code),
    name VARCHAR(100) NOT NULL,
    ip_address INET,
    port INTEGER,
    username VARCHAR(100),
    password_hash VARCHAR(255),
    status VARCHAR(20) DEFAULT 'active',
    config JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2.3 系统管理表

**系统配置表 (system_config)**
```sql
CREATE TABLE system_config (
    key VARCHAR(100) PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    config_type VARCHAR(50) DEFAULT 'string',
    is_encrypted BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

**审计日志表 (audit_logs)**
```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operation VARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
    table_name VARCHAR(100) NOT NULL,
    record_id VARCHAR(100),
    old_values JSONB,
    new_values JSONB,
    user_id VARCHAR(100),
    user_ip INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);
```

**API访问日志表 (api_access_logs)**
```sql
CREATE TABLE api_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id VARCHAR(100),
    method VARCHAR(10) NOT NULL,
    path VARCHAR(500) NOT NULL,
    query_params JSONB,
    request_body JSONB,
    response_status INTEGER,
    response_time_ms INTEGER,
    user_id VARCHAR(100),
    api_key_id VARCHAR(100),
    client_ip INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
) PARTITION BY RANGE (created_at);
```

## 4. 分区策略设计

### 4.1 时间分区策略

#### 4.1.1 交易数据分区

```sql
-- 创建交易数据月度分区
CREATE TABLE transaction_data_y2025m01 PARTITION OF transaction_data
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE transaction_data_y2025m02 PARTITION OF transaction_data
    FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

-- 自动分区创建函数
CREATE OR REPLACE FUNCTION create_monthly_partitions(
    table_name TEXT,
    start_date DATE,
    end_date DATE
) RETURNS VOID AS $$
DECLARE
    current_date DATE := start_date;
    next_date DATE;
    partition_name TEXT;
BEGIN
    WHILE current_date < end_date LOOP
        next_date := current_date + INTERVAL '1 month';
        partition_name := table_name || '_y' || EXTRACT(YEAR FROM current_date) ||
                        'm' || LPAD(EXTRACT(MONTH FROM current_date)::TEXT, 2, '0');

        EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I
                       FOR VALUES FROM (%L) TO (%L)',
                       partition_name, table_name, current_date, next_date);

        current_date := next_date;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 创建未来12个月的分区
SELECT create_monthly_partitions('transaction_data', CURRENT_DATE, CURRENT_DATE + INTERVAL '12 months');
SELECT create_monthly_partitions('vehicle_data', CURRENT_DATE, CURRENT_DATE + INTERVAL '12 months');
SELECT create_monthly_partitions('audit_logs', CURRENT_DATE, CURRENT_DATE + INTERVAL '12 months');
SELECT create_monthly_partitions('api_access_logs', CURRENT_DATE, CURRENT_DATE + INTERVAL '12 months');
```

#### 4.1.2 分区维护策略

```sql
-- 分区维护存储过程
CREATE OR REPLACE FUNCTION maintain_partitions() RETURNS VOID AS $$
DECLARE
    partition_record RECORD;
    partition_date DATE;
    retention_date DATE := CURRENT_DATE - INTERVAL '7 years';
BEGIN
    -- 创建未来分区
    PERFORM create_monthly_partitions('transaction_data', CURRENT_DATE + INTERVAL '11 months', CURRENT_DATE + INTERVAL '13 months');
    PERFORM create_monthly_partitions('vehicle_data', CURRENT_DATE + INTERVAL '11 months', CURRENT_DATE + INTERVAL '13 months');

    -- 删除过期分区
    FOR partition_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'transaction_data_y%'
           OR tablename LIKE 'vehicle_data_y%'
    LOOP
        -- 提取分区日期
        partition_date := TO_DATE(
            SUBSTRING(partition_record.tablename FROM '\d{4}m\d{2}'),
            'YYYYmMM'
        );

        -- 如果分区超过保留期，则删除
        IF partition_date < retention_date THEN
            EXECUTE format('DROP TABLE IF EXISTS %I.%I',
                          partition_record.schemaname,
                          partition_record.tablename);
            RAISE NOTICE 'Dropped partition: %', partition_record.tablename;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务（需要pg_cron扩展）
SELECT cron.schedule('partition-maintenance', '0 2 1 * *', 'SELECT maintain_partitions();');
```

### 4.2 数据归档策略

```sql
-- 数据归档表
CREATE TABLE archived_transaction_data (
    LIKE transaction_data INCLUDING ALL
);

CREATE TABLE archived_vehicle_data (
    LIKE vehicle_data INCLUDING ALL
);

-- 数据归档函数
CREATE OR REPLACE FUNCTION archive_old_data(archive_before_date DATE) RETURNS INTEGER AS $$
DECLARE
    archived_count INTEGER := 0;
BEGIN
    -- 归档交易数据
    WITH archived AS (
        DELETE FROM transaction_data
        WHERE created_at < archive_before_date
        RETURNING *
    )
    INSERT INTO archived_transaction_data SELECT * FROM archived;

    GET DIAGNOSTICS archived_count = ROW_COUNT;

    -- 归档摄像头数据
    WITH archived AS (
        DELETE FROM vehicle_data
        WHERE created_at < archive_before_date
        RETURNING *
    )
    INSERT INTO archived_vehicle_data SELECT * FROM archived;

    GET DIAGNOSTICS archived_count = archived_count + ROW_COUNT;

    RETURN archived_count;
END;
$$ LANGUAGE plpgsql;
```

## 5. 索引设计

### 5.1 主要索引策略

#### 5.1.1 交易数据表索引

```sql
-- 时间戳索引（用于时间范围查询）
CREATE INDEX idx_transaction_data_timestamp ON transaction_data (timestamp);

-- 门架代码索引（用于按门架查询）
CREATE INDEX idx_transaction_data_gantry_code ON transaction_data (gantry_code);

-- 车牌号索引（用于车牌查询）
CREATE INDEX idx_transaction_data_plate_no ON transaction_data (plate_no) WHERE plate_no IS NOT NULL;

-- 复合索引（门架+时间，常用查询组合）
CREATE INDEX idx_transaction_data_gantry_timestamp ON transaction_data (gantry_code, timestamp);

-- 复合索引（车牌+时间，用于车辆轨迹查询）
CREATE INDEX idx_transaction_data_plate_timestamp ON transaction_data (plate_no, timestamp) WHERE plate_no IS NOT NULL;

-- MAC ID索引（用于设备查询）
CREATE INDEX idx_transaction_data_macid ON transaction_data (macid) WHERE macid IS NOT NULL;

-- 费用索引（用于费用统计）
CREATE INDEX idx_transaction_data_fees ON transaction_data (recievable_fee, actual_fee) WHERE recievable_fee IS NOT NULL;

-- JSONB元数据索引
CREATE INDEX idx_transaction_data_metadata ON transaction_data USING GIN (metadata);

-- 创建时间索引（用于数据管理）
CREATE INDEX idx_transaction_data_created_at ON transaction_data (created_at);
```

#### 5.1.2 摄像头数据表索引

```sql
-- 时间戳索引
CREATE INDEX idx_vehicle_data_timestamp ON vehicle_data (timestamp);

-- 门架代码索引
CREATE INDEX idx_vehicle_data_gantry_code ON vehicle_data (gantry_code);

-- 摄像头代码索引
CREATE INDEX idx_vehicle_data_camera_code ON vehicle_data (camera_code);

-- 车牌号索引
CREATE INDEX idx_vehicle_data_plate_no ON vehicle_data (plate_no) WHERE plate_no IS NOT NULL;

-- 复合索引（摄像头+时间）
CREATE INDEX idx_vehicle_data_camera_timestamp ON vehicle_data (camera_code, timestamp);

-- 复合索引（门架+摄像头+时间）
CREATE INDEX idx_vehicle_data_gantry_camera_timestamp ON vehicle_data (gantry_code, camera_code, timestamp);

-- 车辆类型索引
CREATE INDEX idx_vehicle_data_vehicle_type ON vehicle_data (vehicle_type) WHERE vehicle_type IS NOT NULL;

-- 置信度索引（用于数据质量分析）
CREATE INDEX idx_vehicle_data_confidence ON vehicle_data (confidence_score) WHERE confidence_score IS NOT NULL;

-- 图像数据存在性索引
CREATE INDEX idx_vehicle_data_has_image ON vehicle_data ((image_bytes IS NOT NULL));

-- JSONB元数据索引
CREATE INDEX idx_vehicle_data_metadata ON vehicle_data USING GIN (metadata);
```

#### 5.1.3 配置表索引

```sql
-- 门架表索引
CREATE INDEX idx_gantries_location ON gantries (location);
CREATE INDEX idx_gantries_is_active ON gantries (is_active);
CREATE INDEX idx_gantries_config ON gantries USING GIN (config);

-- 摄像头表索引
CREATE INDEX idx_cameras_gantry_code ON cameras (gantry_code);
CREATE INDEX idx_cameras_ip_address ON cameras (ip_address);
CREATE INDEX idx_cameras_status ON cameras (status);
CREATE INDEX idx_cameras_config ON cameras USING GIN (config);

-- 系统配置表索引
CREATE INDEX idx_system_config_type ON system_config (config_type);
CREATE INDEX idx_system_config_updated_at ON system_config (updated_at);
```

#### 5.1.4 日志表索引

```sql
-- 审计日志索引
CREATE INDEX idx_audit_logs_table_name ON audit_logs (table_name);
CREATE INDEX idx_audit_logs_operation ON audit_logs (operation);
CREATE INDEX idx_audit_logs_user_id ON audit_logs (user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs (created_at);
CREATE INDEX idx_audit_logs_record_id ON audit_logs (record_id);

-- API访问日志索引
CREATE INDEX idx_api_access_logs_path ON api_access_logs (path);
CREATE INDEX idx_api_access_logs_method ON api_access_logs (method);
CREATE INDEX idx_api_access_logs_status ON api_access_logs (response_status);
CREATE INDEX idx_api_access_logs_user_id ON api_access_logs (user_id);
CREATE INDEX idx_api_access_logs_client_ip ON api_access_logs (client_ip);
CREATE INDEX idx_api_access_logs_created_at ON api_access_logs (created_at);
```

### 5.2 索引维护策略

```sql
-- 索引统计信息更新
CREATE OR REPLACE FUNCTION update_table_statistics() RETURNS VOID AS $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE schemaname = 'public'
          AND (tablename LIKE 'transaction_data%'
               OR tablename LIKE 'vehicle_data%'
               OR tablename IN ('gantries', 'cameras', 'system_config'))
    LOOP
        EXECUTE format('ANALYZE %I.%I', table_record.schemaname, table_record.tablename);
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 定期更新统计信息
SELECT cron.schedule('update-statistics', '0 3 * * *', 'SELECT update_table_statistics();');

-- 重建索引函数
CREATE OR REPLACE FUNCTION rebuild_indexes(table_name TEXT) RETURNS VOID AS $$
DECLARE
    index_record RECORD;
BEGIN
    FOR index_record IN
        SELECT indexname
        FROM pg_indexes
        WHERE tablename = table_name
          AND schemaname = 'public'
    LOOP
        EXECUTE format('REINDEX INDEX %I', index_record.indexname);
        RAISE NOTICE 'Rebuilt index: %', index_record.indexname;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## 6. 数据完整性约束

### 6.1 外键约束

```sql
-- 摄像头表外键约束
ALTER TABLE cameras
ADD CONSTRAINT fk_cameras_gantry_code
FOREIGN KEY (gantry_code) REFERENCES gantries(code)
ON UPDATE CASCADE ON DELETE RESTRICT;

-- 可选：如果需要严格的数据完整性
-- ALTER TABLE transaction_data
-- ADD CONSTRAINT fk_transaction_data_gantry_code
-- FOREIGN KEY (gantry_code) REFERENCES gantries(code);

-- ALTER TABLE vehicle_data
-- ADD CONSTRAINT fk_vehicle_data_gantry_code
-- FOREIGN KEY (gantry_code) REFERENCES gantries(code);

-- ALTER TABLE vehicle_data
-- ADD CONSTRAINT fk_vehicle_data_camera_code
-- FOREIGN KEY (camera_code) REFERENCES cameras(code);
```

### 6.2 检查约束

```sql
-- 交易数据检查约束
ALTER TABLE transaction_data
ADD CONSTRAINT chk_transaction_data_timestamp
CHECK (timestamp > 0);

ALTER TABLE transaction_data
ADD CONSTRAINT chk_transaction_data_fees
CHECK (recievable_fee >= 0 AND actual_fee >= 0);

ALTER TABLE transaction_data
ADD CONSTRAINT chk_transaction_data_plate_no
CHECK (plate_no IS NULL OR LENGTH(plate_no) >= 7);

-- 摄像头数据检查约束
ALTER TABLE vehicle_data
ADD CONSTRAINT chk_vehicle_data_timestamp
CHECK (timestamp > 0);

ALTER TABLE vehicle_data
ADD CONSTRAINT chk_vehicle_data_confidence
CHECK (confidence_score IS NULL OR (confidence_score >= 0 AND confidence_score <= 1));

ALTER TABLE vehicle_data
ADD CONSTRAINT chk_vehicle_data_plate_no
CHECK (plate_no IS NULL OR LENGTH(plate_no) >= 7);

-- 摄像头配置检查约束
ALTER TABLE cameras
ADD CONSTRAINT chk_cameras_port
CHECK (port > 0 AND port <= 65535);

ALTER TABLE cameras
ADD CONSTRAINT chk_cameras_status
CHECK (status IN ('active', 'inactive', 'maintenance', 'error'));
```

### 6.3 唯一性约束

```sql
-- 门架代码唯一性（已通过主键保证）
-- 摄像头代码唯一性（已通过主键保证）

-- 系统配置键唯一性（已通过主键保证）

-- 可选：业务唯一性约束
-- 同一时间戳、同一门架的交易数据唯一性（如果业务需要）
-- CREATE UNIQUE INDEX idx_transaction_data_unique_per_gantry_time
-- ON transaction_data (gantry_code, timestamp, plate_no)
-- WHERE plate_no IS NOT NULL;
```

## 7. 性能优化策略

### 7.1 查询优化

#### 7.1.1 常用查询模式优化

```sql
-- 1. 时间范围查询优化
-- 使用分区裁剪和索引
EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM transaction_data
WHERE created_at >= '2025-08-01'
  AND created_at < '2025-09-01'
  AND gantry_code = 'G001';

-- 2. 车牌轨迹查询优化
-- 使用复合索引
EXPLAIN (ANALYZE, BUFFERS)
SELECT gantry_code, timestamp, recievable_fee
FROM transaction_data
WHERE plate_no = '京A12345'
  AND timestamp BETWEEN 1692518400000 AND 1692604800000
ORDER BY timestamp;

-- 3. 统计查询优化
-- 使用部分索引和聚合
EXPLAIN (ANALYZE, BUFFERS)
SELECT gantry_code,
       COUNT(*) as transaction_count,
       SUM(recievable_fee) as total_fee
FROM transaction_data
WHERE created_at >= CURRENT_DATE - INTERVAL '1 day'
  AND recievable_fee IS NOT NULL
GROUP BY gantry_code;
```

#### 7.1.2 查询计划优化

```sql
-- 设置查询优化参数
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET pg_stat_statements.track = 'all';
ALTER SYSTEM SET pg_stat_statements.max = 10000;

-- 启用并行查询
ALTER SYSTEM SET max_parallel_workers_per_gather = 4;
ALTER SYSTEM SET max_parallel_workers = 8;
ALTER SYSTEM SET parallel_tuple_cost = 0.1;
ALTER SYSTEM SET parallel_setup_cost = 1000;

-- 优化工作内存
ALTER SYSTEM SET work_mem = '256MB';
ALTER SYSTEM SET maintenance_work_mem = '1GB';

-- 优化检查点
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
```

### 7.2 存储优化

#### 7.2.1 表空间配置

```sql
-- 创建不同性能等级的表空间
CREATE TABLESPACE hot_data
LOCATION '/data/ssd/postgresql/hot_data';

CREATE TABLESPACE warm_data
LOCATION '/data/sata/postgresql/warm_data';

CREATE TABLESPACE cold_data
LOCATION '/data/archive/postgresql/cold_data';

-- 将热数据表移动到SSD表空间
ALTER TABLE transaction_data SET TABLESPACE hot_data;
ALTER TABLE vehicle_data SET TABLESPACE hot_data;

-- 将配置表移动到SSD表空间
ALTER TABLE gantries SET TABLESPACE hot_data;
ALTER TABLE cameras SET TABLESPACE hot_data;
ALTER TABLE system_config SET TABLESPACE hot_data;

-- 将归档数据移动到归档表空间
ALTER TABLE archived_transaction_data SET TABLESPACE cold_data;
ALTER TABLE archived_vehicle_data SET TABLESPACE cold_data;
```

#### 7.2.2 压缩和存储优化

```sql
-- 启用表压缩（需要安装pg_squeeze扩展）
-- CREATE EXTENSION IF NOT EXISTS pg_squeeze;

-- 对大表启用TOAST压缩
ALTER TABLE transaction_data ALTER COLUMN metadata SET STORAGE EXTENDED;
ALTER TABLE vehicle_data ALTER COLUMN image_bytes SET STORAGE EXTENDED;
ALTER TABLE vehicle_data ALTER COLUMN metadata SET STORAGE EXTENDED;

-- 设置填充因子优化更新性能
ALTER TABLE transaction_data SET (fillfactor = 90);
ALTER TABLE vehicle_data SET (fillfactor = 90);

-- 对只读表设置更高的填充因子
ALTER TABLE gantries SET (fillfactor = 100);
ALTER TABLE cameras SET (fillfactor = 95);
```

### 7.3 连接池配置

```sql
-- PgBouncer配置示例
-- /etc/pgbouncer/pgbouncer.ini
/*
[databases]
hcs_db = host=localhost port=5432 dbname=hcs_db

[pgbouncer]
listen_port = 6432
listen_addr = *
auth_type = md5
auth_file = /etc/pgbouncer/userlist.txt
admin_users = postgres
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 25
max_db_connections = 100
reserve_pool_size = 5
reserve_pool_timeout = 3
server_reset_query = DISCARD ALL
server_check_delay = 10
*/
```

## 8. 监控和维护

### 8.1 性能监控

#### 8.1.1 监控视图

```sql
-- 创建性能监控视图
CREATE OR REPLACE VIEW v_database_performance AS
SELECT
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
WHERE schemaname = 'public'
ORDER BY n_live_tup DESC;

-- 索引使用情况监控
CREATE OR REPLACE VIEW v_index_usage AS
SELECT
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    CASE
        WHEN idx_scan = 0 THEN 'UNUSED'
        WHEN idx_scan < 100 THEN 'LOW_USAGE'
        ELSE 'NORMAL'
    END as usage_status
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- 慢查询监控
CREATE OR REPLACE VIEW v_slow_queries AS
SELECT
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
WHERE mean_time > 1000  -- 超过1秒的查询
ORDER BY mean_time DESC
LIMIT 20;
```

#### 8.1.2 监控函数

```sql
-- 数据库大小监控
CREATE OR REPLACE FUNCTION get_database_size_info()
RETURNS TABLE(
    database_name TEXT,
    size_mb NUMERIC,
    size_pretty TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        datname::TEXT,
        ROUND(pg_database_size(datname) / 1024.0 / 1024.0, 2),
        pg_size_pretty(pg_database_size(datname))
    FROM pg_database
    WHERE datname = current_database();
END;
$$ LANGUAGE plpgsql;

-- 表大小监控
CREATE OR REPLACE FUNCTION get_table_size_info()
RETURNS TABLE(
    schema_name TEXT,
    table_name TEXT,
    row_count BIGINT,
    table_size_mb NUMERIC,
    index_size_mb NUMERIC,
    total_size_mb NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        schemaname::TEXT,
        tablename::TEXT,
        n_live_tup,
        ROUND(pg_relation_size(schemaname||'.'||tablename) / 1024.0 / 1024.0, 2),
        ROUND(pg_indexes_size(schemaname||'.'||tablename) / 1024.0 / 1024.0, 2),
        ROUND(pg_total_relation_size(schemaname||'.'||tablename) / 1024.0 / 1024.0, 2)
    FROM pg_stat_user_tables
    WHERE schemaname = 'public'
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$ LANGUAGE plpgsql;

-- 连接监控
CREATE OR REPLACE FUNCTION get_connection_info()
RETURNS TABLE(
    state TEXT,
    count BIGINT,
    max_connections INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COALESCE(state, 'unknown')::TEXT,
        COUNT(*),
        (SELECT setting::INTEGER FROM pg_settings WHERE name = 'max_connections')
    FROM pg_stat_activity
    WHERE pid <> pg_backend_pid()
    GROUP BY state, (SELECT setting FROM pg_settings WHERE name = 'max_connections')
    ORDER BY count DESC;
END;
$$ LANGUAGE plpgsql;
```

### 8.2 自动化维护

#### 8.2.1 VACUUM和ANALYZE策略

```sql
-- 自定义VACUUM策略
CREATE OR REPLACE FUNCTION smart_vacuum_analyze() RETURNS VOID AS $$
DECLARE
    table_record RECORD;
    dead_tuple_ratio NUMERIC;
BEGIN
    FOR table_record IN
        SELECT schemaname, tablename, n_live_tup, n_dead_tup
        FROM pg_stat_user_tables
        WHERE schemaname = 'public'
          AND n_live_tup > 0
    LOOP
        dead_tuple_ratio := table_record.n_dead_tup::NUMERIC / table_record.n_live_tup::NUMERIC;

        -- 如果死元组比例超过20%，执行VACUUM
        IF dead_tuple_ratio > 0.2 THEN
            EXECUTE format('VACUUM ANALYZE %I.%I', table_record.schemaname, table_record.tablename);
            RAISE NOTICE 'VACUUM ANALYZE executed for table: %.%', table_record.schemaname, table_record.tablename;
        -- 如果死元组比例超过10%，只执行ANALYZE
        ELSIF dead_tuple_ratio > 0.1 THEN
            EXECUTE format('ANALYZE %I.%I', table_record.schemaname, table_record.tablename);
            RAISE NOTICE 'ANALYZE executed for table: %.%', table_record.schemaname, table_record.tablename;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 定期执行维护任务
SELECT cron.schedule('smart-vacuum', '0 2 * * *', 'SELECT smart_vacuum_analyze();');
```

#### 8.2.2 日志清理策略

```sql
-- 日志清理函数
CREATE OR REPLACE FUNCTION cleanup_old_logs() RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
    retention_days INTEGER := 90;
BEGIN
    -- 清理API访问日志
    DELETE FROM api_access_logs
    WHERE created_at < CURRENT_DATE - INTERVAL '90 days';
    GET DIAGNOSTICS deleted_count = ROW_COUNT;

    RAISE NOTICE 'Deleted % old API access log records', deleted_count;

    -- 清理审计日志（保留更长时间）
    DELETE FROM audit_logs
    WHERE created_at < CURRENT_DATE - INTERVAL '2 years';
    GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;

    RAISE NOTICE 'Total deleted log records: %', deleted_count;

    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 定期清理日志
SELECT cron.schedule('cleanup-logs', '0 3 * * 0', 'SELECT cleanup_old_logs();');
```

## 9. 备份和恢复策略

### 9.1 备份策略

#### 9.1.1 全量备份

```bash
#!/bin/bash
# 全量备份脚本 - backup_full.sh

BACKUP_DIR="/backup/postgresql"
DB_NAME="hcs_db"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/full_backup_${DATE}.sql"

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 执行全量备份
pg_dump -h localhost -U postgres -d ${DB_NAME} \
    --verbose \
    --format=custom \
    --compress=9 \
    --file=${BACKUP_FILE}

# 验证备份文件
if [ $? -eq 0 ]; then
    echo "Full backup completed successfully: ${BACKUP_FILE}"

    # 保留最近7天的全量备份
    find ${BACKUP_DIR} -name "full_backup_*.sql" -mtime +7 -delete

    # 上传到远程存储（可选）
    # aws s3 cp ${BACKUP_FILE} s3://hcs-backup-bucket/postgresql/
else
    echo "Full backup failed!"
    exit 1
fi
```

#### 9.1.2 增量备份（WAL归档）

```bash
#!/bin/bash
# WAL归档脚本 - archive_wal.sh

WAL_FILE=$1
WAL_PATH=$2
ARCHIVE_DIR="/backup/postgresql/wal_archive"

# 创建归档目录
mkdir -p ${ARCHIVE_DIR}

# 复制WAL文件到归档目录
cp ${WAL_PATH} ${ARCHIVE_DIR}/${WAL_FILE}

if [ $? -eq 0 ]; then
    echo "WAL file archived: ${WAL_FILE}"
else
    echo "WAL archiving failed: ${WAL_FILE}"
    exit 1
fi
```

```sql
-- PostgreSQL配置（postgresql.conf）
-- 启用WAL归档
wal_level = replica
archive_mode = on
archive_command = '/path/to/archive_wal.sh %f %p'
archive_timeout = 300  -- 5分钟

-- 设置WAL保留
wal_keep_segments = 100
max_wal_senders = 3
```

### 9.2 恢复策略

#### 9.2.1 时间点恢复（PITR）

```bash
#!/bin/bash
# 时间点恢复脚本 - restore_pitr.sh

BACKUP_FILE="/backup/postgresql/full_backup_20250820_020000.sql"
RECOVERY_TARGET_TIME="2025-08-20 14:30:00"
DATA_DIR="/var/lib/postgresql/15/main"
ARCHIVE_DIR="/backup/postgresql/wal_archive"

# 停止PostgreSQL服务
systemctl stop postgresql

# 清空数据目录
rm -rf ${DATA_DIR}/*

# 恢复基础备份
pg_restore -h localhost -U postgres -d template1 -C ${BACKUP_FILE}

# 创建recovery.conf
cat > ${DATA_DIR}/recovery.conf << EOF
restore_command = 'cp ${ARCHIVE_DIR}/%f %p'
recovery_target_time = '${RECOVERY_TARGET_TIME}'
recovery_target_action = 'promote'
EOF

# 启动PostgreSQL服务
systemctl start postgresql

echo "Point-in-time recovery completed to: ${RECOVERY_TARGET_TIME}"
```

#### 9.2.2 灾难恢复流程

```sql
-- 灾难恢复检查清单
/*
1. 评估损坏程度
   - 检查数据文件完整性
   - 检查WAL文件可用性
   - 评估数据丢失范围

2. 选择恢复策略
   - 完全恢复：使用最新备份 + WAL重放
   - 时间点恢复：恢复到特定时间点
   - 部分恢复：只恢复特定表或数据

3. 执行恢复操作
   - 准备恢复环境
   - 恢复基础备份
   - 应用WAL日志
   - 验证数据完整性

4. 验证和测试
   - 数据完整性检查
   - 应用功能测试
   - 性能基准测试
*/

-- 数据完整性检查函数
CREATE OR REPLACE FUNCTION check_data_integrity()
RETURNS TABLE(
    table_name TEXT,
    expected_count BIGINT,
    actual_count BIGINT,
    integrity_status TEXT
) AS $$
DECLARE
    table_record RECORD;
    expected_count BIGINT;
    actual_count BIGINT;
BEGIN
    FOR table_record IN
        SELECT tablename
        FROM pg_tables
        WHERE schemaname = 'public'
          AND tablename IN ('transaction_data', 'vehicle_data', 'gantries', 'cameras')
    LOOP
        -- 获取预期记录数（从备份元数据或监控系统）
        -- 这里简化为使用统计信息
        SELECT n_live_tup INTO expected_count
        FROM pg_stat_user_tables
        WHERE tablename = table_record.tablename;

        -- 获取实际记录数
        EXECUTE format('SELECT COUNT(*) FROM %I', table_record.tablename) INTO actual_count;

        RETURN QUERY SELECT
            table_record.tablename::TEXT,
            expected_count,
            actual_count,
            CASE
                WHEN actual_count = expected_count THEN 'OK'
                WHEN actual_count < expected_count THEN 'DATA_LOSS'
                ELSE 'UNEXPECTED_DATA'
            END::TEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

## 10. 安全策略

### 10.1 访问控制

```sql
-- 创建角色和用户
CREATE ROLE hcs_readonly;
CREATE ROLE hcs_readwrite;
CREATE ROLE hcs_admin;

-- 只读权限
GRANT CONNECT ON DATABASE hcs_db TO hcs_readonly;
GRANT USAGE ON SCHEMA public TO hcs_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO hcs_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO hcs_readonly;

-- 读写权限
GRANT CONNECT ON DATABASE hcs_db TO hcs_readwrite;
GRANT USAGE ON SCHEMA public TO hcs_readwrite;
GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA public TO hcs_readwrite;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO hcs_readwrite;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE ON TABLES TO hcs_readwrite;

-- 管理员权限
GRANT CONNECT ON DATABASE hcs_db TO hcs_admin;
GRANT ALL PRIVILEGES ON DATABASE hcs_db TO hcs_admin;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO hcs_admin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO hcs_admin;

-- 创建应用用户
CREATE USER hcs_app_user WITH PASSWORD 'secure_password_here';
GRANT hcs_readwrite TO hcs_app_user;

CREATE USER hcs_readonly_user WITH PASSWORD 'readonly_password_here';
GRANT hcs_readonly TO hcs_readonly_user;

CREATE USER hcs_admin_user WITH PASSWORD 'admin_password_here';
GRANT hcs_admin TO hcs_admin_user;
```

### 10.2 数据加密

```sql
-- 启用数据加密（需要配置TDE或使用加密文件系统）
-- 敏感字段加密示例

-- 创建加密函数
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 密码加密存储
UPDATE cameras
SET password_hash = crypt(password_hash, gen_salt('bf', 8))
WHERE password_hash IS NOT NULL;

-- 敏感配置加密
CREATE OR REPLACE FUNCTION encrypt_config_value(plain_text TEXT, key_text TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN encode(pgp_sym_encrypt(plain_text, key_text), 'base64');
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION decrypt_config_value(encrypted_text TEXT, key_text TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN pgp_sym_decrypt(decode(encrypted_text, 'base64'), key_text);
END;
$$ LANGUAGE plpgsql;
```

### 10.3 审计配置

```sql
-- 启用审计日志（需要安装pgaudit扩展）
-- CREATE EXTENSION IF NOT EXISTS pgaudit;

-- 配置审计参数
-- ALTER SYSTEM SET pgaudit.log = 'write, ddl, role';
-- ALTER SYSTEM SET pgaudit.log_catalog = off;
-- ALTER SYSTEM SET pgaudit.log_parameter = on;
-- ALTER SYSTEM SET pgaudit.log_relation = on;

-- 创建审计触发器
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (operation, table_name, record_id, new_values, user_id)
        VALUES ('INSERT', TG_TABLE_NAME, NEW.id::TEXT, row_to_json(NEW), current_user);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (operation, table_name, record_id, old_values, new_values, user_id)
        VALUES ('UPDATE', TG_TABLE_NAME, NEW.id::TEXT, row_to_json(OLD), row_to_json(NEW), current_user);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (operation, table_name, record_id, old_values, user_id)
        VALUES ('DELETE', TG_TABLE_NAME, OLD.id::TEXT, row_to_json(OLD), current_user);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 为关键表创建审计触发器
CREATE TRIGGER audit_gantries_trigger
    AFTER INSERT OR UPDATE OR DELETE ON gantries
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_cameras_trigger
    AFTER INSERT OR UPDATE OR DELETE ON cameras
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_system_config_trigger
    AFTER INSERT OR UPDATE OR DELETE ON system_config
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

## 11. 总结

数据库详细设计文档为数据采集系统提供了完整的数据存储解决方案。本设计文档详细描述了：

1. **完整的数据模型**: 从概念模型到物理实现的完整数据库设计
2. **高效的分区策略**: 基于时间的分区设计，支持大数据量的高效存储和查询
3. **优化的索引设计**: 针对业务查询模式的索引优化策略
4. **严格的数据完整性**: 外键约束、检查约束和业务规则约束
5. **全面的性能优化**: 查询优化、存储优化和连接池配置
6. **完善的监控维护**: 性能监控、自动化维护和健康检查
7. **可靠的备份恢复**: 全量备份、增量备份和灾难恢复策略
8. **严密的安全控制**: 访问控制、数据加密和审计日志

该设计确保了数据库系统的高性能、高可用性、高安全性和高可维护性，为整个数据采集系统提供了坚实的数据存储基础。通过分区、索引和优化策略，能够支持大规模数据的高效存储和快速查询，满足系统的性能和容量要求。
