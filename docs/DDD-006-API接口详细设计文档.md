# DDD-006 API接口详细设计文档

**文档编号：** DDD-006  
**文档版本：** v1.0  
**创建日期：** 2025-08-20  
**最后更新：** 2025-08-20  
**文档状态：** 草稿  
**设计负责人：** 接口设计团队  
**对应PRD：** PRD-001-数据采集系统产品需求文档.md  
**上级设计：** DDD-001-系统总体架构详细设计文档.md

---

## 1. 接口概述

### 1.1 接口目的
API接口模块定义了数据采集系统内部各模块间的接口规范，以及对外提供的RESTful API服务，确保系统各组件间的标准化通信和外部系统的集成能力。

### 1.2 需求追溯
本模块对应PRD-001中的以下需求：
- **系统集成**: 提供标准化的API接口供外部系统调用
- **数据查询**: 支持灵活的数据查询和检索功能
- **系统监控**: 提供系统状态和指标查询接口
- **配置管理**: 支持动态配置更新和管理
- **安全认证**: 提供安全的API访问控制

### 1.3 接口分类
- **内部接口**: 模块间通信接口（Rust trait定义）
- **RESTful API**: 对外HTTP接口服务
- **WebSocket API**: 实时数据推送接口
- **管理接口**: 系统管理和配置接口
- **监控接口**: 系统监控和指标接口

## 2. 接口架构设计

### 2.1 接口架构图

```mermaid
graph TB
    subgraph "外部客户端"
        WEB[Web管理界面]
        MOB[移动应用]
        EXT[外部系统]
        MON[监控系统]
    end

    subgraph "API网关层"
        GW[API Gateway<br/>路由和认证]
        LB[负载均衡器]
        RL[限流器]
    end

    subgraph "API服务层"
        subgraph "RESTful API"
            REST1[数据查询API]
            REST2[系统管理API]
            REST3[配置管理API]
            REST4[监控API]
        end
        
        subgraph "WebSocket API"
            WS1[实时数据推送]
            WS2[系统状态推送]
            WS3[告警推送]
        end
        
        subgraph "GraphQL API"
            GQL1[统一查询接口]
            GQL2[数据聚合接口]
        end
    end

    subgraph "业务逻辑层"
        BL1[数据服务]
        BL2[配置服务]
        BL3[监控服务]
        BL4[认证服务]
    end

    subgraph "数据访问层"
        DA1[存储访问]
        DA2[缓存访问]
        DA3[外部服务访问]
    end

    subgraph "内部模块"
        MOD1[数据采集模块]
        MOD2[数据处理模块]
        MOD3[数据传输模块]
        MOD4[数据存储模块]
    end

    WEB --> GW
    MOB --> GW
    EXT --> GW
    MON --> GW

    GW --> LB
    LB --> RL
    RL --> REST1
    RL --> REST2
    RL --> REST3
    RL --> REST4
    RL --> WS1
    RL --> WS2
    RL --> WS3
    RL --> GQL1
    RL --> GQL2

    REST1 --> BL1
    REST2 --> BL2
    REST3 --> BL2
    REST4 --> BL3
    WS1 --> BL1
    WS2 --> BL3
    WS3 --> BL3
    GQL1 --> BL1
    GQL2 --> BL1

    BL1 --> DA1
    BL2 --> DA2
    BL3 --> DA1
    BL4 --> DA3

    DA1 --> MOD4
    BL1 --> MOD1
    BL1 --> MOD2
    BL1 --> MOD3
```

### 2.2 接口调用流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Gateway as API网关
    participant Auth as 认证服务
    participant API as API服务
    participant Business as 业务逻辑
    participant Storage as 数据存储

    Client->>Gateway: HTTP请求
    Gateway->>Auth: 验证Token
    Auth->>Gateway: 认证结果
    
    alt 认证成功
        Gateway->>API: 转发请求
        API->>Business: 调用业务逻辑
        Business->>Storage: 数据操作
        Storage->>Business: 返回结果
        Business->>API: 处理结果
        API->>Gateway: 响应数据
        Gateway->>Client: HTTP响应
    else 认证失败
        Gateway->>Client: 401 Unauthorized
    end
```

## 3. 内部接口设计

### 3.1 数据采集接口

```rust
// api/src/collector.rs
use async_trait::async_trait;
use anyhow::Result;
use types::NormalizedData;

/// 数据采集器接口
#[async_trait]
pub trait DataCollectorApi: Send + Sync {
    /// 启动数据采集
    async fn start_collection(&mut self) -> Result<CollectionStatus>;
    
    /// 停止数据采集
    async fn stop_collection(&mut self) -> Result<CollectionStatus>;
    
    /// 获取采集状态
    async fn get_status(&self) -> Result<CollectionStatus>;
    
    /// 获取采集统计信息
    async fn get_statistics(&self) -> Result<CollectionStatistics>;
    
    /// 配置采集参数
    async fn configure(&mut self, config: CollectionConfig) -> Result<()>;
    
    /// 获取最新采集的数据
    async fn get_latest_data(&self, limit: Option<usize>) -> Result<Vec<NormalizedData>>;
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct CollectionStatus {
    pub is_running: bool,
    pub active_collectors: u32,
    pub last_collection_time: Option<u64>,
    pub error_message: Option<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct CollectionStatistics {
    pub total_collected: u64,
    pub success_rate: f64,
    pub average_processing_time: f64,
    pub errors_count: u64,
    pub last_error: Option<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct CollectionConfig {
    pub enabled_collectors: Vec<String>,
    pub collection_interval: u64,
    pub batch_size: usize,
    pub timeout_seconds: u64,
}
```

### 3.2 数据处理接口

```rust
// api/src/processor.rs
use async_trait::async_trait;
use anyhow::Result;
use types::{NormalizedData, ProcessedData};

/// 数据处理器接口
#[async_trait]
pub trait DataProcessorApi: Send + Sync {
    /// 处理单条数据
    async fn process_data(&mut self, data: NormalizedData) -> Result<ProcessedData>;
    
    /// 批量处理数据
    async fn process_batch(&mut self, data: Vec<NormalizedData>) -> Result<Vec<ProcessedData>>;
    
    /// 获取处理状态
    async fn get_status(&self) -> Result<ProcessingStatus>;
    
    /// 获取处理统计信息
    async fn get_statistics(&self) -> Result<ProcessingStatistics>;
    
    /// 配置处理参数
    async fn configure(&mut self, config: ProcessingConfig) -> Result<()>;
    
    /// 获取处理队列状态
    async fn get_queue_status(&self) -> Result<QueueStatus>;
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ProcessingStatus {
    pub is_processing: bool,
    pub queue_size: usize,
    pub processing_rate: f64,
    pub last_processing_time: Option<u64>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ProcessingStatistics {
    pub total_processed: u64,
    pub success_count: u64,
    pub error_count: u64,
    pub average_processing_time: f64,
    pub validation_errors: u64,
    pub cleaning_actions: u64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ProcessingConfig {
    pub validation_enabled: bool,
    pub cleaning_enabled: bool,
    pub batch_size: usize,
    pub timeout_seconds: u64,
    pub quality_threshold: f64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct QueueStatus {
    pub pending_count: usize,
    pub processing_count: usize,
    pub completed_count: u64,
    pub failed_count: u64,
}
```

### 3.3 数据传输接口

```rust
// api/src/transport.rs
use async_trait::async_trait;
use anyhow::Result;
use types::NormalizedData;

/// 数据传输接口
#[async_trait]
pub trait DataTransportApi: Send + Sync {
    /// 发送数据
    async fn send_data(&mut self, data: NormalizedData) -> Result<TransmissionResult>;
    
    /// 批量发送数据
    async fn send_batch(&mut self, data: Vec<NormalizedData>) -> Result<Vec<TransmissionResult>>;
    
    /// 获取传输状态
    async fn get_status(&self) -> Result<TransportStatus>;
    
    /// 获取传输统计信息
    async fn get_statistics(&self) -> Result<TransportStatistics>;
    
    /// 配置传输参数
    async fn configure(&mut self, config: TransportConfig) -> Result<()>;
    
    /// 测试连接
    async fn test_connection(&self) -> Result<ConnectionTestResult>;
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TransmissionResult {
    pub success: bool,
    pub message_id: String,
    pub bytes_sent: usize,
    pub response_time_ms: u64,
    pub error: Option<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TransportStatus {
    pub is_connected: bool,
    pub active_connections: u32,
    pub queue_size: usize,
    pub last_transmission_time: Option<u64>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TransportStatistics {
    pub total_sent: u64,
    pub success_count: u64,
    pub error_count: u64,
    pub bytes_sent: u64,
    pub average_response_time: f64,
    pub throughput_per_second: f64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TransportConfig {
    pub enabled_transports: Vec<String>,
    pub retry_attempts: u32,
    pub timeout_seconds: u64,
    pub batch_size: usize,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ConnectionTestResult {
    pub success: bool,
    pub response_time_ms: u64,
    pub error: Option<String>,
    pub details: std::collections::HashMap<String, String>,
}
```

### 3.4 数据存储接口

```rust
// api/src/storage.rs
use async_trait::async_trait;
use anyhow::Result;
use types::NormalizedData;

/// 数据存储接口
#[async_trait]
pub trait DataStorageApi: Send + Sync {
    /// 存储数据
    async fn store_data(&mut self, data: NormalizedData) -> Result<StorageResult>;
    
    /// 批量存储数据
    async fn store_batch(&mut self, data: Vec<NormalizedData>) -> Result<Vec<StorageResult>>;
    
    /// 查询数据
    async fn query_data(&self, query: StorageQuery) -> Result<QueryResult>;
    
    /// 删除数据
    async fn delete_data(&mut self, query: StorageQuery) -> Result<DeleteResult>;
    
    /// 获取存储状态
    async fn get_status(&self) -> Result<StorageStatus>;
    
    /// 获取存储统计信息
    async fn get_statistics(&self) -> Result<StorageStatistics>;
    
    /// 配置存储参数
    async fn configure(&mut self, config: StorageConfig) -> Result<()>;
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct StorageResult {
    pub success: bool,
    pub storage_id: String,
    pub bytes_stored: usize,
    pub storage_location: String,
    pub error: Option<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct QueryResult {
    pub data: Vec<NormalizedData>,
    pub total_count: u64,
    pub page_info: PageInfo,
    pub query_time_ms: u64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PageInfo {
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
    pub has_next_page: bool,
    pub has_previous_page: bool,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct DeleteResult {
    pub success: bool,
    pub deleted_count: u64,
    pub error: Option<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct StorageQuery {
    pub filters: Vec<QueryFilter>,
    pub sort_by: Option<String>,
    pub sort_order: SortOrder,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub time_range: Option<TimeRange>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct QueryFilter {
    pub field: String,
    pub operator: FilterOperator,
    pub value: serde_json::Value,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub enum FilterOperator {
    Equal,
    NotEqual,
    GreaterThan,
    LessThan,
    GreaterThanOrEqual,
    LessThanOrEqual,
    Like,
    In,
    NotIn,
    Between,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub enum SortOrder {
    Ascending,
    Descending,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TimeRange {
    pub start: chrono::DateTime<chrono::Utc>,
    pub end: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct StorageStatus {
    pub is_available: bool,
    pub used_space_bytes: u64,
    pub available_space_bytes: u64,
    pub total_records: u64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct StorageStatistics {
    pub total_stored: u64,
    pub total_queried: u64,
    pub total_deleted: u64,
    pub storage_efficiency: f64,
    pub average_query_time: f64,
    pub last_backup_time: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct StorageConfig {
    pub enabled_storages: Vec<String>,
    pub backup_enabled: bool,
    pub compression_enabled: bool,
    pub retention_days: u32,
}
```

## 4. RESTful API设计

### 4.1 API基础规范

**基础URL**: `https://api.hcs.example.com/v1`

**认证方式**: Bearer Token (JWT)

**请求格式**: JSON

**响应格式**: JSON

**HTTP状态码**:
- 200: 成功
- 201: 创建成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 429: 请求过于频繁
- 500: 服务器内部错误

### 4.2 数据查询API

#### 4.2.1 查询交易数据

```http
GET /api/v1/data/transactions
```

**查询参数**:
```json
{
  "gantry_code": "G001",
  "start_time": "2025-08-20T00:00:00Z",
  "end_time": "2025-08-20T23:59:59Z",
  "plate_no": "京A12345",
  "vehicle_type": "小型车",
  "page": 1,
  "page_size": 100,
  "sort_by": "timestamp",
  "sort_order": "desc"
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": "uuid-1234",
        "gantry_code": "G001",
        "timestamp": **********000,
        "plate_no": "京A12345",
        "vehicle_type": "小型车",
        "macid": "MAC001",
        "recievable_fee": 500,
        "actual_fee": 500,
        "created_at": "2025-08-20T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 100,
      "total_count": 1500,
      "total_pages": 15,
      "has_next": true,
      "has_previous": false
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

#### 4.2.2 查询摄像头数据

```http
GET /api/v1/data/vehicles
```

**查询参数**:
```json
{
  "camera_code": "CAM001",
  "start_time": "2025-08-20T00:00:00Z",
  "end_time": "2025-08-20T23:59:59Z",
  "plate_no": "京A12345",
  "include_image": false,
  "page": 1,
  "page_size": 50
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": "uuid-5678",
        "gantry_code": "G001",
        "timestamp": **********000,
        "camera_code": "CAM001",
        "plate_no": "京A12345",
        "plate_type": "蓝牌",
        "plate_color": "蓝色",
        "vehicle_type": "小型车",
        "vehicle_cat": "客车",
        "image_url": "/api/v1/images/uuid-5678",
        "created_at": "2025-08-20T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 50,
      "total_count": 800,
      "total_pages": 16,
      "has_next": true,
      "has_previous": false
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

#### 4.2.3 获取单条数据详情

```http
GET /api/v1/data/{id}
```

**路径参数**:
- `id`: 数据唯一标识符

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "uuid-1234",
    "gantry_code": "G001",
    "timestamp": **********000,
    "plate_no": "京A12345",
    "vehicle_type": "小型车",
    "camera_code": "CAM001",
    "macid": "MAC001",
    "recievable_fee": 500,
    "actual_fee": 500,
    "image_bytes": null,
    "metadata": {
      "processing_time_ms": 150,
      "validation_score": 0.95,
      "data_source": "transaction"
    },
    "created_at": "2025-08-20T12:00:00Z",
    "updated_at": "2025-08-20T12:00:00Z"
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

### 4.3 系统管理API

#### 4.3.1 获取系统状态

```http
GET /api/v1/system/status
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "system_status": "running",
    "uptime_seconds": 86400,
    "version": "1.0.0",
    "modules": {
      "collector": {
        "status": "running",
        "active_collectors": 3,
        "last_collection": "2025-08-20T11:59:30Z"
      },
      "processor": {
        "status": "running",
        "queue_size": 150,
        "processing_rate": 850.5
      },
      "transport": {
        "status": "running",
        "active_connections": 2,
        "success_rate": 99.8
      },
      "storage": {
        "status": "running",
        "used_space_gb": 245.6,
        "available_space_gb": 754.4
      }
    },
    "health_checks": {
      "database": "healthy",
      "file_system": "healthy",
      "network": "healthy",
      "external_services": "healthy"
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

#### 4.3.2 获取系统统计信息

```http
GET /api/v1/system/statistics
```

**查询参数**:
```json
{
  "time_range": "24h",  // 1h, 24h, 7d, 30d
  "metrics": ["throughput", "error_rate", "response_time"]
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "time_range": "24h",
    "summary": {
      "total_processed": 2150000,
      "success_rate": 99.85,
      "average_response_time_ms": 125.6,
      "peak_throughput": 1250.8,
      "error_count": 3225
    },
    "metrics": {
      "throughput": [
        {
          "timestamp": "2025-08-20T00:00:00Z",
          "value": 980.5
        },
        {
          "timestamp": "2025-08-20T01:00:00Z",
          "value": 1050.2
        }
      ],
      "error_rate": [
        {
          "timestamp": "2025-08-20T00:00:00Z",
          "value": 0.12
        },
        {
          "timestamp": "2025-08-20T01:00:00Z",
          "value": 0.08
        }
      ],
      "response_time": [
        {
          "timestamp": "2025-08-20T00:00:00Z",
          "value": 128.5
        },
        {
          "timestamp": "2025-08-20T01:00:00Z",
          "value": 122.3
        }
      ]
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

### 4.4 配置管理API

#### 4.4.1 获取系统配置

```http
GET /api/v1/config
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "collector": {
      "enabled_collectors": ["transaction", "image"],
      "collection_interval": 1000,
      "batch_size": 100
    },
    "processor": {
      "validation_enabled": true,
      "cleaning_enabled": true,
      "quality_threshold": 0.8
    },
    "transport": {
      "enabled_transports": ["tcp", "serial"],
      "retry_attempts": 3,
      "timeout_seconds": 30
    },
    "storage": {
      "enabled_storages": ["file", "database"],
      "backup_enabled": true,
      "retention_days": 365
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

#### 4.4.2 更新系统配置

```http
PUT /api/v1/config
```

**请求体**:
```json
{
  "collector": {
    "collection_interval": 500,
    "batch_size": 200
  },
  "processor": {
    "quality_threshold": 0.85
  }
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "Configuration updated successfully",
  "data": {
    "updated_fields": [
      "collector.collection_interval",
      "collector.batch_size",
      "processor.quality_threshold"
    ],
    "restart_required": false
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

### 4.5 监控API

#### 4.5.1 获取实时指标

```http
GET /api/v1/metrics/realtime
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "timestamp": "2025-08-20T12:00:00Z",
    "metrics": {
      "system": {
        "cpu_usage_percent": 45.2,
        "memory_usage_percent": 62.8,
        "disk_usage_percent": 24.5,
        "network_io_mbps": 125.6
      },
      "application": {
        "active_connections": 15,
        "queue_size": 245,
        "processing_rate": 985.2,
        "error_rate": 0.15
      },
      "business": {
        "transactions_per_minute": 850,
        "vehicles_per_minute": 420,
        "data_quality_score": 0.92,
        "storage_growth_mb_per_hour": 156.8
      }
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

#### 4.5.2 获取告警信息

```http
GET /api/v1/alerts
```

**查询参数**:
```json
{
  "severity": "high",  // low, medium, high, critical
  "status": "active",  // active, resolved, acknowledged
  "start_time": "2025-08-20T00:00:00Z",
  "end_time": "2025-08-20T23:59:59Z",
  "page": 1,
  "page_size": 20
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [
      {
        "id": "alert-001",
        "title": "Storage space warning",
        "description": "Storage usage exceeded 80% threshold",
        "severity": "high",
        "status": "active",
        "source": "storage_monitor",
        "created_at": "2025-08-20T11:30:00Z",
        "updated_at": "2025-08-20T11:30:00Z",
        "metadata": {
          "current_usage": "85.2%",
          "threshold": "80%",
          "affected_storage": "file_storage"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total_count": 5,
      "total_pages": 1,
      "has_next": false,
      "has_previous": false
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

## 5. WebSocket API设计

### 5.1 连接建立

**连接URL**: `wss://api.hcs.example.com/v1/ws`

**认证**: 通过查询参数传递token
```
wss://api.hcs.example.com/v1/ws?token=your_jwt_token
```

### 5.2 消息格式

**客户端请求格式**:
```json
{
  "id": "request-uuid",
  "type": "subscribe|unsubscribe|query",
  "channel": "channel_name",
  "payload": {}
}
```

**服务端响应格式**:
```json
{
  "id": "request-uuid",
  "type": "response|notification|error",
  "channel": "channel_name",
  "payload": {},
  "timestamp": "2025-08-20T12:00:00Z"
}
```

### 5.3 实时数据推送

#### 5.3.1 订阅实时数据

**客户端请求**:
```json
{
  "id": "req-001",
  "type": "subscribe",
  "channel": "realtime_data",
  "payload": {
    "filters": {
      "gantry_codes": ["G001", "G002"],
      "data_types": ["transaction", "vehicle"]
    }
  }
}
```

**服务端确认**:
```json
{
  "id": "req-001",
  "type": "response",
  "channel": "realtime_data",
  "payload": {
    "status": "subscribed",
    "subscription_id": "sub-001"
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

**实时数据推送**:
```json
{
  "type": "notification",
  "channel": "realtime_data",
  "payload": {
    "data": {
      "id": "uuid-1234",
      "gantry_code": "G001",
      "timestamp": **********000,
      "plate_no": "京A12345",
      "vehicle_type": "小型车",
      "data_type": "transaction"
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

#### 5.3.2 订阅系统状态

**客户端请求**:
```json
{
  "id": "req-002",
  "type": "subscribe",
  "channel": "system_status",
  "payload": {
    "modules": ["collector", "processor", "transport", "storage"],
    "interval": 5000
  }
}
```

**系统状态推送**:
```json
{
  "type": "notification",
  "channel": "system_status",
  "payload": {
    "status": {
      "collector": {
        "status": "running",
        "throughput": 985.2,
        "error_rate": 0.15
      },
      "processor": {
        "status": "running",
        "queue_size": 245,
        "processing_rate": 850.5
      }
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

#### 5.3.3 订阅告警通知

**客户端请求**:
```json
{
  "id": "req-003",
  "type": "subscribe",
  "channel": "alerts",
  "payload": {
    "severity_levels": ["high", "critical"]
  }
}
```

**告警推送**:
```json
{
  "type": "notification",
  "channel": "alerts",
  "payload": {
    "alert": {
      "id": "alert-002",
      "title": "High error rate detected",
      "description": "Error rate exceeded 5% in the last 5 minutes",
      "severity": "high",
      "source": "processor",
      "created_at": "2025-08-20T12:00:00Z",
      "metadata": {
        "current_error_rate": "6.2%",
        "threshold": "5%",
        "affected_module": "data_processor"
      }
    }
  },
  "timestamp": "2025-08-20T12:00:00Z"
}
```

### 5.4 WebSocket实现

```rust
// api/src/websocket.rs
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Query, State,
    },
    response::Response,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tokio::sync::{broadcast, RwLock};
use uuid::Uuid;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketMessage {
    pub id: Option<String>,
    pub r#type: MessageType,
    pub channel: String,
    pub payload: serde_json::Value,
    pub timestamp: Option<chrono::DateTime<chrono::Utc>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum MessageType {
    Subscribe,
    Unsubscribe,
    Query,
    Response,
    Notification,
    Error,
}

#[derive(Debug, Clone)]
pub struct WebSocketConnection {
    pub id: String,
    pub subscriptions: Vec<String>,
    pub sender: tokio::sync::mpsc::UnboundedSender<Message>,
}

pub struct WebSocketManager {
    connections: RwLock<HashMap<String, WebSocketConnection>>,
    channels: RwLock<HashMap<String, broadcast::Sender<serde_json::Value>>>,
}

impl WebSocketManager {
    pub fn new() -> Self {
        Self {
            connections: RwLock::new(HashMap::new()),
            channels: RwLock::new(HashMap::new()),
        }
    }

    pub async fn handle_connection(&self, ws: WebSocket, connection_id: String) {
        let (sender, mut receiver) = ws.split();
        let (tx, mut rx) = tokio::sync::mpsc::unbounded_channel();

        // 注册连接
        {
            let mut connections = self.connections.write().await;
            connections.insert(connection_id.clone(), WebSocketConnection {
                id: connection_id.clone(),
                subscriptions: Vec::new(),
                sender: tx,
            });
        }

        // 处理发送消息
        let send_task = tokio::spawn(async move {
            while let Some(msg) = rx.recv().await {
                if sender.send(msg).await.is_err() {
                    break;
                }
            }
        });

        // 处理接收消息
        let manager = self.clone();
        let recv_task = tokio::spawn(async move {
            while let Some(msg) = receiver.next().await {
                if let Ok(msg) = msg {
                    if let Ok(text) = msg.to_text() {
                        if let Ok(ws_msg) = serde_json::from_str::<WebSocketMessage>(text) {
                            manager.handle_message(connection_id.clone(), ws_msg).await;
                        }
                    }
                }
            }
        });

        // 等待任务完成
        tokio::select! {
            _ = send_task => {},
            _ = recv_task => {},
        }

        // 清理连接
        {
            let mut connections = self.connections.write().await;
            connections.remove(&connection_id);
        }
    }

    async fn handle_message(&self, connection_id: String, message: WebSocketMessage) {
        match message.r#type {
            MessageType::Subscribe => {
                self.handle_subscribe(connection_id, message).await;
            }
            MessageType::Unsubscribe => {
                self.handle_unsubscribe(connection_id, message).await;
            }
            MessageType::Query => {
                self.handle_query(connection_id, message).await;
            }
            _ => {}
        }
    }

    async fn handle_subscribe(&self, connection_id: String, message: WebSocketMessage) {
        // 实现订阅逻辑
        let mut connections = self.connections.write().await;
        if let Some(conn) = connections.get_mut(&connection_id) {
            conn.subscriptions.push(message.channel.clone());

            // 发送确认消息
            let response = WebSocketMessage {
                id: message.id,
                r#type: MessageType::Response,
                channel: message.channel,
                payload: serde_json::json!({
                    "status": "subscribed",
                    "subscription_id": Uuid::new_v4().to_string()
                }),
                timestamp: Some(chrono::Utc::now()),
            };

            let _ = conn.sender.send(Message::Text(serde_json::to_string(&response).unwrap()));
        }
    }

    async fn handle_unsubscribe(&self, connection_id: String, message: WebSocketMessage) {
        // 实现取消订阅逻辑
        let mut connections = self.connections.write().await;
        if let Some(conn) = connections.get_mut(&connection_id) {
            conn.subscriptions.retain(|ch| ch != &message.channel);

            // 发送确认消息
            let response = WebSocketMessage {
                id: message.id,
                r#type: MessageType::Response,
                channel: message.channel,
                payload: serde_json::json!({
                    "status": "unsubscribed"
                }),
                timestamp: Some(chrono::Utc::now()),
            };

            let _ = conn.sender.send(Message::Text(serde_json::to_string(&response).unwrap()));
        }
    }

    async fn handle_query(&self, connection_id: String, message: WebSocketMessage) {
        // 实现查询逻辑
        // 这里可以调用相应的业务逻辑来处理查询请求
    }

    pub async fn broadcast_to_channel(&self, channel: &str, data: serde_json::Value) {
        let connections = self.connections.read().await;

        for conn in connections.values() {
            if conn.subscriptions.contains(&channel.to_string()) {
                let notification = WebSocketMessage {
                    id: None,
                    r#type: MessageType::Notification,
                    channel: channel.to_string(),
                    payload: data.clone(),
                    timestamp: Some(chrono::Utc::now()),
                };

                let _ = conn.sender.send(Message::Text(serde_json::to_string(&notification).unwrap()));
            }
        }
    }
}
```

## 6. GraphQL API设计

### 6.1 Schema定义

```graphql
# GraphQL Schema
type Query {
  # 数据查询
  transactions(
    gantryCode: String
    startTime: DateTime
    endTime: DateTime
    plateNo: String
    vehicleType: String
    first: Int
    after: String
  ): TransactionConnection!

  vehicles(
    cameraCode: String
    startTime: DateTime
    endTime: DateTime
    plateNo: String
    includeImage: Boolean = false
    first: Int
    after: String
  ): VehicleConnection!

  # 系统查询
  systemStatus: SystemStatus!
  systemStatistics(timeRange: String!): SystemStatistics!

  # 配置查询
  configuration: Configuration!

  # 监控查询
  realtimeMetrics: RealtimeMetrics!
  alerts(
    severity: AlertSeverity
    status: AlertStatus
    first: Int
    after: String
  ): AlertConnection!
}

type Mutation {
  # 配置更新
  updateConfiguration(input: ConfigurationInput!): ConfigurationUpdateResult!

  # 告警操作
  acknowledgeAlert(id: ID!): Alert!
  resolveAlert(id: ID!): Alert!
}

type Subscription {
  # 实时数据订阅
  realtimeData(filters: RealtimeDataFilters): NormalizedData!

  # 系统状态订阅
  systemStatusUpdates: SystemStatus!

  # 告警订阅
  alertNotifications(severityLevels: [AlertSeverity!]): Alert!
}

# 数据类型定义
type Transaction {
  id: ID!
  gantryCode: String!
  timestamp: DateTime!
  plateNo: String
  vehicleType: String
  macid: String
  vehicleFeeType: String
  recievableFee: Int
  actualFee: Int
  proRecievableFee: Int
  proActualFee: Int
  createdAt: DateTime!
}

type Vehicle {
  id: ID!
  gantryCode: String!
  timestamp: DateTime!
  cameraCode: String!
  plateNo: String
  plateType: String
  plateColor: String
  vehicleType: String
  vehicleCat: String
  vehicleSize: String
  vehicleObject: String
  imageUrl: String
  createdAt: DateTime!
}

type NormalizedData {
  id: ID!
  gantryCode: String!
  timestamp: DateTime!
  plateNo: String
  vehicleType: String
  cameraCode: String
  macid: String
  dataType: DataType!
  createdAt: DateTime!
}

enum DataType {
  TRANSACTION
  VEHICLE
}

# 连接类型（分页）
type TransactionConnection {
  edges: [TransactionEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type TransactionEdge {
  node: Transaction!
  cursor: String!
}

type VehicleConnection {
  edges: [VehicleEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type VehicleEdge {
  node: Vehicle!
  cursor: String!
}

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

# 系统状态类型
type SystemStatus {
  systemStatus: String!
  uptimeSeconds: Int!
  version: String!
  modules: ModuleStatus!
  healthChecks: HealthChecks!
}

type ModuleStatus {
  collector: CollectorStatus!
  processor: ProcessorStatus!
  transport: TransportStatus!
  storage: StorageStatus!
}

type CollectorStatus {
  status: String!
  activeCollectors: Int!
  lastCollection: DateTime
}

type ProcessorStatus {
  status: String!
  queueSize: Int!
  processingRate: Float!
}

type TransportStatus {
  status: String!
  activeConnections: Int!
  successRate: Float!
}

type StorageStatus {
  status: String!
  usedSpaceGb: Float!
  availableSpaceGb: Float!
}

type HealthChecks {
  database: String!
  fileSystem: String!
  network: String!
  externalServices: String!
}

# 统计信息类型
type SystemStatistics {
  timeRange: String!
  summary: StatisticsSummary!
  metrics: MetricsData!
}

type StatisticsSummary {
  totalProcessed: Int!
  successRate: Float!
  averageResponseTimeMs: Float!
  peakThroughput: Float!
  errorCount: Int!
}

type MetricsData {
  throughput: [MetricPoint!]!
  errorRate: [MetricPoint!]!
  responseTime: [MetricPoint!]!
}

type MetricPoint {
  timestamp: DateTime!
  value: Float!
}

# 配置类型
type Configuration {
  collector: CollectorConfig!
  processor: ProcessorConfig!
  transport: TransportConfig!
  storage: StorageConfig!
}

type CollectorConfig {
  enabledCollectors: [String!]!
  collectionInterval: Int!
  batchSize: Int!
}

type ProcessorConfig {
  validationEnabled: Boolean!
  cleaningEnabled: Boolean!
  qualityThreshold: Float!
}

type TransportConfig {
  enabledTransports: [String!]!
  retryAttempts: Int!
  timeoutSeconds: Int!
}

type StorageConfig {
  enabledStorages: [String!]!
  backupEnabled: Boolean!
  retentionDays: Int!
}

# 输入类型
input ConfigurationInput {
  collector: CollectorConfigInput
  processor: ProcessorConfigInput
  transport: TransportConfigInput
  storage: StorageConfigInput
}

input CollectorConfigInput {
  enabledCollectors: [String!]
  collectionInterval: Int
  batchSize: Int
}

input ProcessorConfigInput {
  validationEnabled: Boolean
  cleaningEnabled: Boolean
  qualityThreshold: Float
}

input TransportConfigInput {
  enabledTransports: [String!]
  retryAttempts: Int
  timeoutSeconds: Int
}

input StorageConfigInput {
  enabledStorages: [String!]
  backupEnabled: Boolean
  retentionDays: Int
}

input RealtimeDataFilters {
  gantryCodes: [String!]
  dataTypes: [DataType!]
}

# 告警类型
type Alert {
  id: ID!
  title: String!
  description: String!
  severity: AlertSeverity!
  status: AlertStatus!
  source: String!
  createdAt: DateTime!
  updatedAt: DateTime!
  metadata: JSON
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum AlertStatus {
  ACTIVE
  ACKNOWLEDGED
  RESOLVED
}

type AlertConnection {
  edges: [AlertEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type AlertEdge {
  node: Alert!
  cursor: String!
}

# 实时指标类型
type RealtimeMetrics {
  timestamp: DateTime!
  system: SystemMetrics!
  application: ApplicationMetrics!
  business: BusinessMetrics!
}

type SystemMetrics {
  cpuUsagePercent: Float!
  memoryUsagePercent: Float!
  diskUsagePercent: Float!
  networkIoMbps: Float!
}

type ApplicationMetrics {
  activeConnections: Int!
  queueSize: Int!
  processingRate: Float!
  errorRate: Float!
}

type BusinessMetrics {
  transactionsPerMinute: Int!
  vehiclesPerMinute: Int!
  dataQualityScore: Float!
  storageGrowthMbPerHour: Float!
}

# 配置更新结果
type ConfigurationUpdateResult {
  success: Boolean!
  updatedFields: [String!]!
  restartRequired: Boolean!
  message: String
}

# 自定义标量类型
scalar DateTime
scalar JSON
```

### 6.2 GraphQL查询示例

#### 6.2.1 查询交易数据

```graphql
query GetTransactions($gantryCode: String!, $startTime: DateTime!, $endTime: DateTime!) {
  transactions(
    gantryCode: $gantryCode
    startTime: $startTime
    endTime: $endTime
    first: 100
  ) {
    edges {
      node {
        id
        gantryCode
        timestamp
        plateNo
        vehicleType
        macid
        recievableFee
        actualFee
        createdAt
      }
      cursor
    }
    pageInfo {
      hasNextPage
      hasPreviousPage
      startCursor
      endCursor
    }
    totalCount
  }
}
```

#### 6.2.2 查询系统状态和统计

```graphql
query GetSystemOverview {
  systemStatus {
    systemStatus
    uptimeSeconds
    version
    modules {
      collector {
        status
        activeCollectors
        lastCollection
      }
      processor {
        status
        queueSize
        processingRate
      }
      transport {
        status
        activeConnections
        successRate
      }
      storage {
        status
        usedSpaceGb
        availableSpaceGb
      }
    }
    healthChecks {
      database
      fileSystem
      network
      externalServices
    }
  }

  systemStatistics(timeRange: "24h") {
    summary {
      totalProcessed
      successRate
      averageResponseTimeMs
      peakThroughput
      errorCount
    }
    metrics {
      throughput {
        timestamp
        value
      }
      errorRate {
        timestamp
        value
      }
    }
  }
}
```

#### 6.2.3 订阅实时数据

```graphql
subscription RealtimeDataStream($filters: RealtimeDataFilters!) {
  realtimeData(filters: $filters) {
    id
    gantryCode
    timestamp
    plateNo
    vehicleType
    cameraCode
    macid
    dataType
    createdAt
  }
}
```

## 7. 安全认证设计

### 7.1 JWT认证机制

#### 7.1.1 Token结构

```json
{
  "header": {
    "alg": "RS256",
    "typ": "JWT",
    "kid": "key-id-1"
  },
  "payload": {
    "iss": "hcs-api-server",
    "sub": "user-12345",
    "aud": "hcs-api",
    "exp": **********,
    "iat": **********,
    "jti": "token-uuid",
    "scope": "read:data write:config admin:system",
    "roles": ["operator", "admin"],
    "permissions": [
      "data:read",
      "data:query",
      "config:read",
      "config:write",
      "system:monitor"
    ]
  }
}
```

#### 7.1.2 权限控制

**权限级别**:
- `guest`: 只读访问基础数据
- `operator`: 数据查询和基础操作
- `admin`: 系统配置和管理
- `superadmin`: 完全访问权限

**权限映射**:
```rust
// api/src/auth.rs
use std::collections::HashMap;

pub struct PermissionManager {
    role_permissions: HashMap<String, Vec<String>>,
}

impl PermissionManager {
    pub fn new() -> Self {
        let mut role_permissions = HashMap::new();

        role_permissions.insert("guest".to_string(), vec![
            "data:read".to_string(),
        ]);

        role_permissions.insert("operator".to_string(), vec![
            "data:read".to_string(),
            "data:query".to_string(),
            "system:status".to_string(),
        ]);

        role_permissions.insert("admin".to_string(), vec![
            "data:read".to_string(),
            "data:query".to_string(),
            "data:export".to_string(),
            "config:read".to_string(),
            "config:write".to_string(),
            "system:status".to_string(),
            "system:monitor".to_string(),
        ]);

        role_permissions.insert("superadmin".to_string(), vec![
            "*".to_string(), // 所有权限
        ]);

        Self { role_permissions }
    }

    pub fn check_permission(&self, roles: &[String], required_permission: &str) -> bool {
        for role in roles {
            if let Some(permissions) = self.role_permissions.get(role) {
                if permissions.contains(&"*".to_string()) ||
                   permissions.contains(&required_permission.to_string()) {
                    return true;
                }
            }
        }
        false
    }
}
```

### 7.2 API密钥认证

#### 7.2.1 API密钥格式

```
hcs_ak_1234567890abcdef1234567890abcdef
```

**密钥结构**:
- `hcs`: 系统标识
- `ak`: 密钥类型 (Access Key)
- `1234567890abcdef1234567890abcdef`: 32位十六进制密钥

#### 7.2.2 密钥管理

```rust
// api/src/api_key.rs
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiKey {
    pub id: String,
    pub key: String,
    pub name: String,
    pub description: String,
    pub permissions: Vec<String>,
    pub rate_limit: Option<RateLimit>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub last_used_at: Option<DateTime<Utc>>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimit {
    pub requests_per_minute: u32,
    pub requests_per_hour: u32,
    pub requests_per_day: u32,
}

pub struct ApiKeyManager {
    keys: std::collections::HashMap<String, ApiKey>,
}

impl ApiKeyManager {
    pub fn new() -> Self {
        Self {
            keys: std::collections::HashMap::new(),
        }
    }

    pub fn validate_key(&mut self, key: &str) -> Option<&ApiKey> {
        if let Some(api_key) = self.keys.get_mut(key) {
            if api_key.is_active {
                if let Some(expires_at) = api_key.expires_at {
                    if Utc::now() > expires_at {
                        return None;
                    }
                }
                api_key.last_used_at = Some(Utc::now());
                return Some(api_key);
            }
        }
        None
    }

    pub fn check_rate_limit(&self, key: &str) -> bool {
        // 实现速率限制检查
        true
    }
}
```

### 7.3 OAuth 2.0集成

#### 7.3.1 授权流程

```mermaid
sequenceDiagram
    participant Client as 客户端应用
    participant AuthServer as 认证服务器
    participant API as API服务器
    participant Resource as 资源服务器

    Client->>AuthServer: 1. 授权请求
    AuthServer->>Client: 2. 授权码
    Client->>AuthServer: 3. 交换访问令牌
    AuthServer->>Client: 4. 访问令牌
    Client->>API: 5. API请求 + 访问令牌
    API->>AuthServer: 6. 验证令牌
    AuthServer->>API: 7. 令牌有效性
    API->>Resource: 8. 访问资源
    Resource->>API: 9. 返回数据
    API->>Client: 10. API响应
```

#### 7.3.2 OAuth配置

```yaml
# config/oauth.yaml
oauth:
  enabled: true
  providers:
    - name: "internal"
      client_id: "hcs-api-client"
      client_secret: "${OAUTH_CLIENT_SECRET}"
      authorization_url: "https://auth.hcs.example.com/oauth/authorize"
      token_url: "https://auth.hcs.example.com/oauth/token"
      userinfo_url: "https://auth.hcs.example.com/oauth/userinfo"
      scopes: ["read", "write", "admin"]

    - name: "ldap"
      enabled: true
      server_url: "ldap://ldap.company.com:389"
      bind_dn: "cn=admin,dc=company,dc=com"
      bind_password: "${LDAP_PASSWORD}"
      user_base_dn: "ou=users,dc=company,dc=com"
      group_base_dn: "ou=groups,dc=company,dc=com"

  token:
    access_token_ttl: 3600  # 1小时
    refresh_token_ttl: 86400  # 24小时
    signing_algorithm: "RS256"
    public_key_path: "/etc/hcs/oauth_public.pem"
    private_key_path: "/etc/hcs/oauth_private.pem"
```

## 8. 错误处理和响应格式

### 8.1 统一错误响应格式

```json
{
  "code": 400,
  "message": "Bad Request",
  "error": {
    "type": "VALIDATION_ERROR",
    "details": "Invalid query parameters",
    "field_errors": [
      {
        "field": "start_time",
        "message": "Invalid date format",
        "code": "INVALID_FORMAT"
      }
    ]
  },
  "request_id": "req-uuid-1234",
  "timestamp": "2025-08-20T12:00:00Z"
}
```

### 8.2 错误类型定义

```rust
// api/src/error.rs
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiError {
    pub code: u16,
    pub message: String,
    pub error: ErrorDetails,
    pub request_id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorDetails {
    pub r#type: ErrorType,
    pub details: String,
    pub field_errors: Option<Vec<FieldError>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorType {
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    NotFoundError,
    ConflictError,
    RateLimitError,
    InternalServerError,
    ServiceUnavailableError,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldError {
    pub field: String,
    pub message: String,
    pub code: String,
}

impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        let status_code = StatusCode::from_u16(self.code).unwrap_or(StatusCode::INTERNAL_SERVER_ERROR);
        (status_code, Json(self)).into_response()
    }
}

impl ApiError {
    pub fn validation_error(details: String, field_errors: Vec<FieldError>) -> Self {
        Self {
            code: 400,
            message: "Bad Request".to_string(),
            error: ErrorDetails {
                r#type: ErrorType::ValidationError,
                details,
                field_errors: Some(field_errors),
            },
            request_id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn authentication_error(details: String) -> Self {
        Self {
            code: 401,
            message: "Unauthorized".to_string(),
            error: ErrorDetails {
                r#type: ErrorType::AuthenticationError,
                details,
                field_errors: None,
            },
            request_id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn authorization_error(details: String) -> Self {
        Self {
            code: 403,
            message: "Forbidden".to_string(),
            error: ErrorDetails {
                r#type: ErrorType::AuthorizationError,
                details,
                field_errors: None,
            },
            request_id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn not_found_error(details: String) -> Self {
        Self {
            code: 404,
            message: "Not Found".to_string(),
            error: ErrorDetails {
                r#type: ErrorType::NotFoundError,
                details,
                field_errors: None,
            },
            request_id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn rate_limit_error(details: String) -> Self {
        Self {
            code: 429,
            message: "Too Many Requests".to_string(),
            error: ErrorDetails {
                r#type: ErrorType::RateLimitError,
                details,
                field_errors: None,
            },
            request_id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
        }
    }

    pub fn internal_server_error(details: String) -> Self {
        Self {
            code: 500,
            message: "Internal Server Error".to_string(),
            error: ErrorDetails {
                r#type: ErrorType::InternalServerError,
                details,
                field_errors: None,
            },
            request_id: uuid::Uuid::new_v4().to_string(),
            timestamp: chrono::Utc::now(),
        }
    }
}
```

### 8.3 请求验证

```rust
// api/src/validation.rs
use serde::{Deserialize, Serialize};
use validator::{Validate, ValidationError};

#[derive(Debug, Deserialize, Validate)]
pub struct QueryParams {
    #[validate(length(min = 1, max = 50))]
    pub gantry_code: Option<String>,

    #[validate(custom = "validate_datetime")]
    pub start_time: Option<String>,

    #[validate(custom = "validate_datetime")]
    pub end_time: Option<String>,

    #[validate(length(min = 1, max = 20))]
    pub plate_no: Option<String>,

    #[validate(range(min = 1, max = 1000))]
    pub page_size: Option<u32>,

    #[validate(range(min = 1))]
    pub page: Option<u32>,
}

fn validate_datetime(datetime_str: &str) -> Result<(), ValidationError> {
    chrono::DateTime::parse_from_rfc3339(datetime_str)
        .map_err(|_| ValidationError::new("invalid_datetime_format"))?;
    Ok(())
}

pub fn validate_query_params(params: &QueryParams) -> Result<(), Vec<FieldError>> {
    match params.validate() {
        Ok(_) => Ok(()),
        Err(errors) => {
            let field_errors = errors
                .field_errors()
                .iter()
                .flat_map(|(field, errors)| {
                    errors.iter().map(move |error| FieldError {
                        field: field.to_string(),
                        message: error.message.as_ref()
                            .map(|m| m.to_string())
                            .unwrap_or_else(|| format!("Validation failed for field: {}", field)),
                        code: error.code.to_string(),
                    })
                })
                .collect();
            Err(field_errors)
        }
    }
}
```

## 9. 性能优化

### 9.1 缓存策略

#### 9.1.1 Redis缓存配置

```yaml
# config/cache.yaml
cache:
  enabled: true
  provider: "redis"
  redis:
    host: "localhost"
    port: 6379
    password: "${REDIS_PASSWORD}"
    database: 0
    pool_size: 10

  strategies:
    - name: "query_cache"
      ttl: 300  # 5分钟
      max_size: 10000
      patterns:
        - "/api/v1/data/transactions*"
        - "/api/v1/data/vehicles*"

    - name: "config_cache"
      ttl: 3600  # 1小时
      max_size: 1000
      patterns:
        - "/api/v1/config*"

    - name: "status_cache"
      ttl: 60  # 1分钟
      max_size: 100
      patterns:
        - "/api/v1/system/status"
        - "/api/v1/metrics/realtime"
```

#### 9.1.2 缓存实现

```rust
// api/src/cache.rs
use redis::{Client, Connection, Commands};
use serde::{Deserialize, Serialize};
use std::time::Duration;

pub struct CacheManager {
    client: Client,
    default_ttl: Duration,
}

impl CacheManager {
    pub fn new(redis_url: &str, default_ttl: Duration) -> Result<Self, redis::RedisError> {
        let client = Client::open(redis_url)?;
        Ok(Self {
            client,
            default_ttl,
        })
    }

    pub async fn get<T>(&self, key: &str) -> Result<Option<T>, redis::RedisError>
    where
        T: for<'de> Deserialize<'de>,
    {
        let mut conn = self.client.get_connection()?;
        let cached_data: Option<String> = conn.get(key)?;

        match cached_data {
            Some(data) => {
                let deserialized: T = serde_json::from_str(&data)
                    .map_err(|_| redis::RedisError::from((redis::ErrorKind::TypeError, "Deserialization failed")))?;
                Ok(Some(deserialized))
            }
            None => Ok(None),
        }
    }

    pub async fn set<T>(&self, key: &str, value: &T, ttl: Option<Duration>) -> Result<(), redis::RedisError>
    where
        T: Serialize,
    {
        let mut conn = self.client.get_connection()?;
        let serialized = serde_json::to_string(value)
            .map_err(|_| redis::RedisError::from((redis::ErrorKind::TypeError, "Serialization failed")))?;

        let ttl_seconds = ttl.unwrap_or(self.default_ttl).as_secs() as usize;
        conn.set_ex(key, serialized, ttl_seconds)?;
        Ok(())
    }

    pub async fn delete(&self, key: &str) -> Result<(), redis::RedisError> {
        let mut conn = self.client.get_connection()?;
        conn.del(key)?;
        Ok(())
    }

    pub async fn exists(&self, key: &str) -> Result<bool, redis::RedisError> {
        let mut conn = self.client.get_connection()?;
        let exists: bool = conn.exists(key)?;
        Ok(exists)
    }

    pub fn generate_cache_key(&self, prefix: &str, params: &[(&str, &str)]) -> String {
        let mut key = format!("hcs:{}:", prefix);
        for (k, v) in params {
            key.push_str(&format!("{}:{}:", k, v));
        }
        key.trim_end_matches(':').to_string()
    }
}

// 缓存中间件
pub async fn cache_middleware<B>(
    req: axum::extract::Request<B>,
    next: axum::middleware::Next<B>,
) -> axum::response::Response {
    let cache_manager = req.extensions().get::<CacheManager>().cloned();

    if let Some(cache) = cache_manager {
        let cache_key = generate_request_cache_key(&req);

        // 尝试从缓存获取
        if let Ok(Some(cached_response)) = cache.get::<String>(&cache_key).await {
            return axum::response::Response::builder()
                .status(200)
                .header("content-type", "application/json")
                .header("x-cache", "HIT")
                .body(cached_response.into())
                .unwrap();
        }

        // 执行请求
        let response = next.run(req).await;

        // 缓存响应（如果成功）
        if response.status().is_success() {
            // 这里需要实现响应体的缓存逻辑
        }

        response
    } else {
        next.run(req).await
    }
}

fn generate_request_cache_key<B>(req: &axum::extract::Request<B>) -> String {
    format!("api:{}:{}", req.method(), req.uri().path_and_query().map(|pq| pq.as_str()).unwrap_or(""))
}
```

### 9.2 分页优化

#### 9.2.1 游标分页实现

```rust
// api/src/pagination.rs
use base64::{Engine as _, engine::general_purpose};
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CursorPagination {
    pub first: Option<u32>,
    pub after: Option<String>,
    pub last: Option<u32>,
    pub before: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PageInfo {
    pub has_next_page: bool,
    pub has_previous_page: bool,
    pub start_cursor: Option<String>,
    pub end_cursor: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Connection<T> {
    pub edges: Vec<Edge<T>>,
    pub page_info: PageInfo,
    pub total_count: Option<u64>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Edge<T> {
    pub node: T,
    pub cursor: String,
}

pub struct PaginationHelper;

impl PaginationHelper {
    pub fn encode_cursor(id: &str, timestamp: i64) -> String {
        let cursor_data = format!("{}:{}", timestamp, id);
        general_purpose::STANDARD.encode(cursor_data.as_bytes())
    }

    pub fn decode_cursor(cursor: &str) -> Result<(i64, String), String> {
        let decoded = general_purpose::STANDARD.decode(cursor)
            .map_err(|_| "Invalid cursor format")?;

        let cursor_str = String::from_utf8(decoded)
            .map_err(|_| "Invalid cursor encoding")?;

        let parts: Vec<&str> = cursor_str.split(':').collect();
        if parts.len() != 2 {
            return Err("Invalid cursor structure".to_string());
        }

        let timestamp = parts[0].parse::<i64>()
            .map_err(|_| "Invalid timestamp in cursor")?;
        let id = parts[1].to_string();

        Ok((timestamp, id))
    }

    pub fn build_connection<T>(
        items: Vec<T>,
        total_count: Option<u64>,
        cursor_fn: impl Fn(&T) -> String,
        has_more: bool,
    ) -> Connection<T> {
        let edges: Vec<Edge<T>> = items
            .into_iter()
            .map(|item| {
                let cursor = cursor_fn(&item);
                Edge { node: item, cursor }
            })
            .collect();

        let page_info = PageInfo {
            has_next_page: has_more,
            has_previous_page: false, // 简化实现
            start_cursor: edges.first().map(|e| e.cursor.clone()),
            end_cursor: edges.last().map(|e| e.cursor.clone()),
        };

        Connection {
            edges,
            page_info,
            total_count,
        }
    }
}
```

### 9.3 限流和熔断

#### 9.3.1 限流实现

```rust
// api/src/rate_limit.rs
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;

pub struct RateLimiter {
    windows: Arc<Mutex<HashMap<String, RateLimitWindow>>>,
    default_limit: u32,
    window_duration: Duration,
}

#[derive(Debug, Clone)]
struct RateLimitWindow {
    count: u32,
    window_start: Instant,
}

impl RateLimiter {
    pub fn new(default_limit: u32, window_duration: Duration) -> Self {
        Self {
            windows: Arc::new(Mutex::new(HashMap::new())),
            default_limit,
            window_duration,
        }
    }

    pub async fn check_rate_limit(&self, key: &str, limit: Option<u32>) -> bool {
        let limit = limit.unwrap_or(self.default_limit);
        let now = Instant::now();

        let mut windows = self.windows.lock().await;
        let window = windows.entry(key.to_string()).or_insert(RateLimitWindow {
            count: 0,
            window_start: now,
        });

        // 检查是否需要重置窗口
        if now.duration_since(window.window_start) >= self.window_duration {
            window.count = 0;
            window.window_start = now;
        }

        // 检查是否超过限制
        if window.count >= limit {
            false
        } else {
            window.count += 1;
            true
        }
    }

    pub async fn get_remaining(&self, key: &str, limit: Option<u32>) -> u32 {
        let limit = limit.unwrap_or(self.default_limit);
        let windows = self.windows.lock().await;

        if let Some(window) = windows.get(key) {
            limit.saturating_sub(window.count)
        } else {
            limit
        }
    }
}

// 限流中间件
pub async fn rate_limit_middleware<B>(
    req: axum::extract::Request<B>,
    next: axum::middleware::Next<B>,
) -> axum::response::Response {
    let rate_limiter = req.extensions().get::<RateLimiter>().cloned();

    if let Some(limiter) = rate_limiter {
        // 从请求中提取标识符（IP地址、API密钥等）
        let client_id = extract_client_id(&req);

        if !limiter.check_rate_limit(&client_id, None).await {
            return axum::response::Response::builder()
                .status(429)
                .header("content-type", "application/json")
                .body(serde_json::json!({
                    "code": 429,
                    "message": "Too Many Requests",
                    "error": {
                        "type": "RATE_LIMIT_ERROR",
                        "details": "Rate limit exceeded"
                    }
                }).to_string().into())
                .unwrap();
        }

        let response = next.run(req).await;

        // 添加限流头部
        let remaining = limiter.get_remaining(&client_id, None).await;
        axum::response::Response::builder()
            .status(response.status())
            .header("x-ratelimit-remaining", remaining.to_string())
            .header("x-ratelimit-limit", limiter.default_limit.to_string())
            .body(response.into_body())
            .unwrap()
    } else {
        next.run(req).await
    }
}

fn extract_client_id<B>(req: &axum::extract::Request<B>) -> String {
    // 优先使用API密钥
    if let Some(api_key) = req.headers().get("x-api-key") {
        if let Ok(key_str) = api_key.to_str() {
            return format!("api_key:{}", key_str);
        }
    }

    // 使用IP地址作为后备
    if let Some(forwarded_for) = req.headers().get("x-forwarded-for") {
        if let Ok(ip_str) = forwarded_for.to_str() {
            return format!("ip:{}", ip_str.split(',').next().unwrap_or("unknown").trim());
        }
    }

    "unknown".to_string()
}
```

## 10. 监控和日志

### 10.1 API监控指标

```rust
// api/src/metrics.rs
use prometheus::{Counter, Histogram, Gauge, Registry};
use std::sync::Arc;

pub struct ApiMetrics {
    pub request_total: Counter,
    pub request_duration: Histogram,
    pub active_connections: Gauge,
    pub error_total: Counter,
    pub cache_hits: Counter,
    pub cache_misses: Counter,
}

impl ApiMetrics {
    pub fn new(registry: &Registry) -> Self {
        let request_total = Counter::new("api_requests_total", "Total number of API requests")
            .expect("Failed to create request_total counter");

        let request_duration = Histogram::new("api_request_duration_seconds", "API request duration")
            .expect("Failed to create request_duration histogram");

        let active_connections = Gauge::new("api_active_connections", "Number of active connections")
            .expect("Failed to create active_connections gauge");

        let error_total = Counter::new("api_errors_total", "Total number of API errors")
            .expect("Failed to create error_total counter");

        let cache_hits = Counter::new("api_cache_hits_total", "Total number of cache hits")
            .expect("Failed to create cache_hits counter");

        let cache_misses = Counter::new("api_cache_misses_total", "Total number of cache misses")
            .expect("Failed to create cache_misses counter");

        registry.register(Box::new(request_total.clone())).unwrap();
        registry.register(Box::new(request_duration.clone())).unwrap();
        registry.register(Box::new(active_connections.clone())).unwrap();
        registry.register(Box::new(error_total.clone())).unwrap();
        registry.register(Box::new(cache_hits.clone())).unwrap();
        registry.register(Box::new(cache_misses.clone())).unwrap();

        Self {
            request_total,
            request_duration,
            active_connections,
            error_total,
            cache_hits,
            cache_misses,
        }
    }
}

// 监控中间件
pub async fn metrics_middleware<B>(
    req: axum::extract::Request<B>,
    next: axum::middleware::Next<B>,
) -> axum::response::Response {
    let start_time = std::time::Instant::now();
    let method = req.method().clone();
    let path = req.uri().path().to_string();

    let metrics = req.extensions().get::<Arc<ApiMetrics>>().cloned();

    if let Some(metrics) = metrics {
        metrics.request_total.inc();
        metrics.active_connections.inc();
    }

    let response = next.run(req).await;

    if let Some(metrics) = metrics {
        let duration = start_time.elapsed().as_secs_f64();
        metrics.request_duration.observe(duration);
        metrics.active_connections.dec();

        if !response.status().is_success() {
            metrics.error_total.inc();
        }
    }

    response
}
```

### 10.2 结构化日志

```rust
// api/src/logging.rs
use tracing::{info, warn, error, debug};
use serde_json::json;

pub struct ApiLogger;

impl ApiLogger {
    pub fn log_request<B>(req: &axum::extract::Request<B>, request_id: &str) {
        info!(
            request_id = request_id,
            method = %req.method(),
            uri = %req.uri(),
            user_agent = ?req.headers().get("user-agent"),
            "API request received"
        );
    }

    pub fn log_response(
        request_id: &str,
        status: u16,
        duration_ms: u64,
        response_size: Option<usize>,
    ) {
        info!(
            request_id = request_id,
            status = status,
            duration_ms = duration_ms,
            response_size = response_size,
            "API request completed"
        );
    }

    pub fn log_error(request_id: &str, error: &str, context: Option<serde_json::Value>) {
        error!(
            request_id = request_id,
            error = error,
            context = ?context,
            "API request error"
        );
    }

    pub fn log_cache_hit(request_id: &str, cache_key: &str) {
        debug!(
            request_id = request_id,
            cache_key = cache_key,
            "Cache hit"
        );
    }

    pub fn log_cache_miss(request_id: &str, cache_key: &str) {
        debug!(
            request_id = request_id,
            cache_key = cache_key,
            "Cache miss"
        );
    }
}
```

## 11. 总结

API接口模块是数据采集系统的重要对外服务组件，提供了完整的接口服务能力。本设计文档详细描述了：

1. **多层次接口设计**: 内部接口、RESTful API、WebSocket API、GraphQL API的完整设计
2. **统一接口规范**: 标准化的请求响应格式、错误处理和数据结构
3. **安全认证体系**: JWT、API密钥、OAuth 2.0等多种认证方式
4. **性能优化策略**: 缓存、分页、限流、熔断等性能优化机制
5. **实时通信支持**: WebSocket实时数据推送和状态通知
6. **灵活查询能力**: GraphQL统一查询接口和复杂数据聚合
7. **全面监控日志**: API指标收集、结构化日志和性能监控

该设计确保了API接口的高性能、高可用性和高安全性，为外部系统集成和前端应用提供了稳定可靠的接口服务。通过模块化的架构设计，可以根据实际需求灵活扩展和定制接口功能。
