[workspace]
resolver = "2"
members = [
    "crates/image",
    "crates/storage",
    "crates/transaction",
    "crates/transport",
    "crates/types",
    "hcs",
]


[workspace.dependencies]
# Core async runtime
tokio = { version = "1.0", features = ["full"] }
# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
# Error handling
thiserror = "2.0"
anyhow = "1.0"
# Logging
tracing = "0.1"
tracing-subscriber = "0.3"
# Configuration
toml = "0.9"
# Async traits
async-trait = "0.1"
# UUID generation
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = "0.4"
