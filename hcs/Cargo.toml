[package]
name = "hcs"
version = "0.1.0"
edition = "2021"

[dependencies]
# Workspace crates
transaction = { path = "../crates/transaction" }
types = { path = "../crates/types" }
image = { path = "../crates/image" }
storage = { path = "../crates/storage" }
transport = { path = "../crates/transport" }

# External dependencies
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
thiserror = { workspace = true }
anyhow = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
toml = { workspace = true }
chrono = { workspace = true }
