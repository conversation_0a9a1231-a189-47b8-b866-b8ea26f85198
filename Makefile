# Makefile for HCS Rust Project
# 默认目标
.DEFAULT_GOAL := help

# 变量定义
CARGO := cargo
BINARY_NAME := hcs
TARGET_DIR := target
RELEASE_DIR := $(TARGET_DIR)/release
DEBUG_DIR := $(TARGET_DIR)/debug

# 颜色定义
GREEN := \033[0;32m
YELLOW := \033[0;33m
RED := \033[0;31m
NC := \033[0m # No Color

# 帮助信息
.PHONY: help
help: ## 显示帮助信息
	@echo "$(GREEN)HCS 项目 Makefile 命令:$(NC)"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(YELLOW)%-20s$(NC) %s\n", $$1, $$2}'

# 构建相关
.PHONY: build
build: ## 构建项目 (debug模式)
	@echo "$(GREEN)构建项目...$(NC)"
	$(CARGO) build

.PHONY: build-release
build-release: ## 构建项目 (release模式)
	@echo "$(GREEN)构建项目 (release模式)...$(NC)"
	$(CARGO) build --release

.PHONY: build-all
build-all: ## 构建所有crates
	@echo "$(GREEN)构建所有crates...$(NC)"
	$(CARGO) build --workspace

# 运行相关
.PHONY: run
run: ## 运行主程序 (debug模式)
	@echo "$(GREEN)运行 $(BINARY_NAME)...$(NC)"
	$(CARGO) run --bin $(BINARY_NAME)

.PHONY: run-release
run-release: ## 运行主程序 (release模式)
	@echo "$(GREEN)运行 $(BINARY_NAME) (release模式)...$(NC)"
	$(CARGO) run --release --bin $(BINARY_NAME)

# 测试相关
.PHONY: test
test: ## 运行所有测试
	@echo "$(GREEN)运行测试...$(NC)"
	$(CARGO) test

.PHONY: test-verbose
test-verbose: ## 运行测试 (详细输出)
	@echo "$(GREEN)运行测试 (详细输出)...$(NC)"
	$(CARGO) test -- --nocapture

.PHONY: test-workspace
test-workspace: ## 运行workspace所有测试
	@echo "$(GREEN)运行workspace测试...$(NC)"
	$(CARGO) test --workspace

# 代码质量
.PHONY: check
check: ## 检查代码 (不构建)
	@echo "$(GREEN)检查代码...$(NC)"
	$(CARGO) check

.PHONY: check-all
check-all: ## 检查所有crates
	@echo "$(GREEN)检查所有crates...$(NC)"
	$(CARGO) check --workspace

.PHONY: clippy
clippy: ## 运行clippy代码检查
	@echo "$(GREEN)运行clippy...$(NC)"
	$(CARGO) clippy -- -D warnings

.PHONY: fmt
fmt: ## 格式化代码
	@echo "$(GREEN)格式化代码...$(NC)"
	$(CARGO) fmt

.PHONY: fmt-check
fmt-check: ## 检查代码格式
	@echo "$(GREEN)检查代码格式...$(NC)"
	$(CARGO) fmt -- --check

# 清理相关
.PHONY: clean
clean: ## 清理构建文件
	@echo "$(GREEN)清理构建文件...$(NC)"
	$(CARGO) clean

.PHONY: clean-deps
clean-deps: ## 清理依赖缓存
	@echo "$(GREEN)清理依赖缓存...$(NC)"
	$(CARGO) clean
	rm -rf ~/.cargo/registry/index/*
	rm -rf ~/.cargo/registry/cache/*

# 依赖管理
.PHONY: update
update: ## 更新依赖
	@echo "$(GREEN)更新依赖...$(NC)"
	$(CARGO) update

.PHONY: audit
audit: ## 安全审计
	@echo "$(GREEN)运行安全审计...$(NC)"
	$(CARGO) audit

# 文档相关
.PHONY: doc
doc: ## 生成文档
	@echo "$(GREEN)生成文档...$(NC)"
	$(CARGO) doc

.PHONY: doc-open
doc-open: ## 生成并打开文档
	@echo "$(GREEN)生成并打开文档...$(NC)"
	$(CARGO) doc --open

# 开发工具
.PHONY: watch
watch: ## 监视文件变化并自动构建
	@echo "$(GREEN)监视文件变化...$(NC)"
	$(CARGO) watch -x build

.PHONY: watch-test
watch-test: ## 监视文件变化并自动测试
	@echo "$(GREEN)监视文件变化并测试...$(NC)"
	$(CARGO) watch -x test

# 安装相关
.PHONY: install
install: ## 安装二进制文件到系统
	@echo "$(GREEN)安装 $(BINARY_NAME)...$(NC)"
	$(CARGO) install --path hcs

.PHONY: uninstall
uninstall: ## 卸载二进制文件
	@echo "$(GREEN)卸载 $(BINARY_NAME)...$(NC)"
	$(CARGO) uninstall $(BINARY_NAME)

# 组合命令
.PHONY: dev
dev: fmt clippy test ## 开发前检查 (格式化 + clippy + 测试)
	@echo "$(GREEN)开发检查完成!$(NC)"

.PHONY: ci
ci: fmt-check clippy test ## CI检查 (格式检查 + clippy + 测试)
	@echo "$(GREEN)CI检查完成!$(NC)"

.PHONY: release-prep
release-prep: clean fmt clippy test build-release ## 发布准备 (清理 + 格式化 + 检查 + 测试 + 构建)
	@echo "$(GREEN)发布准备完成!$(NC)"

# 信息查看
.PHONY: info
info: ## 显示项目信息
	@echo "$(GREEN)项目信息:$(NC)"
	@echo "  项目名称: $(BINARY_NAME)"
	@echo "  Cargo版本: $$($(CARGO) --version)"
	@echo "  Rust版本: $$(rustc --version)"
	@echo "  目标目录: $(TARGET_DIR)"
	@echo "  Workspace成员:"
	@$(CARGO) metadata --format-version 1 | jq -r '.workspace_members[]' | sed 's/^/    /'

.PHONY: deps
deps: ## 显示依赖树
	@echo "$(GREEN)依赖树:$(NC)"
	$(CARGO) tree

# 特殊目标
.PHONY: all
all: build test ## 构建并测试所有内容

# 确保目录存在
$(TARGET_DIR):
	mkdir -p $(TARGET_DIR)